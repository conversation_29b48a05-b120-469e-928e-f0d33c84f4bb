#!/bin/bash
INSTANCE_PORTS=$MATRIX_INSTANCE_PORTS && INSTANCE_PORTS_ARRAY=(${INSTANCE_PORTS//,/ }) && for EXPORT_PROT in ${INSTANCE_PORTS_ARRAY[@]} ; do export $EXPORT_PROT; done
# 添加port参数开始，可以直接使用如下端口参数
PORT_INFO=`# $main; `
# 添加port参数结束

# jdk url | hdc url on hdc
JDK_URL='hdc.baidu-int.com:9119/home/<USER>/release/toos/jdk1.8.tar.gz'
HDC_URL='hdc.baidu-int.com:9119/home/<USER>/release/hdc'
JAR_NAME=''

# log file
LOG_FILE="/home/<USER>/noah_control.log"
>${LOG_FILE}

# only for start check
function retrycheck() {
    for i in $(seq 1 20);do
        status && return 0
    done
}

function app_deploy() {
    # 启动服务
    cd ./module && mkdir -p ./status/app && supervise -p ./status/app -f "unzip dist.zip"
}

# start
function start() {
    info "ready to start module ..."
    # 环境准备
    [[ -d ./jdk1.8 ]] || ([[ -f ./jdk1.8.tar.gz ]] || wget -nv $JDK_URL && tar zxf ./jdk1.8.tar.gz)
    export JAVA_HOME=/home/<USER>/jdk1.8
    export CLASSPATH=${JAVA_HOME}/lib
    export PATH=${JAVA_HOME}/bin:$PATH
    # config LANG
    export LANG=zh_CN.utf8

    app_deploy && retrycheck

    local ret=$?
    [[ $ret == 0 ]] &&  info "start module $JAR_NAME success !! " || error "start module $JAR_NAME failed "
        return $ret
}

# stop
function stop() {
    # kill supervise
    local sup_pid=$(pgrep supervise)
    [[ -n $sup_pid ]] && kill $sup_pid

    # kill jar
    local pid=$(pgrep java)
    [[ -n $pid ]] && kill $pid
    sleep 2 && status
    local ret=$?
    [[ $ret == 0 ]] && error "stop module $JAR_NAME failed !! " || info "stop module $JAR_NAME success "
    # running return 1 else return 0
    [[ $ret == 0 ]] && return 1 || return 0
}

# run status
# 0 -> running, 1 -> starting, 2 -> stopped
function status() {
    sleep 2
    [[ -n $main ]] && nc -z 127.0.0.1 $main
    local ret=$?
    [[ $ret == 0 ]] && info "running" || info "not running"
    return $ret
}

# help
function help() {
    echo "Usage: bin/noah_contorl start/stop/status/restart"
}

# logger
function logging() {
    local timestamp=$(date)
    if [[ ${VERBOSELY} -eq 1 ]]; then
        # output to the standard error
        echo "[${timestamp}]$@" >&2
    fi
    echo "[${timestamp}]$@" >>${LOG_FILE}
}

# warning
function warning() {
    logging "WARNING: $@"
}

# info
function info() {
    logging "INFO: $@"
}

# error
function error() {
    logging "error: $@"
}

########################control###################################
function main() {
    local ACTION=$1
    local ret_val
    case "${ACTION}" in
    start)
        stop && start
        ret_val=$?
        ;;
    stop)
        status && stop || ret_val=0
        ret_val=$?
        ;;
    restart)
        stop && start
        ret_val=$?
        ;;
    status)
        status
        ret_val=$?
        ;;
    *)
        help
        return 1
        ;;
    esac
    return ${ret_val}
}

main "$@"

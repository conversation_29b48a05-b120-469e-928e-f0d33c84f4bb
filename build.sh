#!/bin/bash

# 创建输出目录
mkdir -p "output"

echo "=== 开始构建主应用 ==="
# 安装主应用依赖
npm i
# 构建主应用
npm run build

echo "=== 开始构建React微应用 ==="
# 进入React微应用目录
cd react-doc-table
# 安装微应用依赖
npm i
# 构建微应用 - 生产模式
npm run build:prod
cd ..

# 复制React微应用构建产物
echo "=== 复制React微应用构建产物到 dist/micro-apps/react-doc-table ==="
mkdir -p ./dist/micro-apps/react-doc-table
cp -r ./react-doc-table/dist/* ./dist/micro-apps/react-doc-table/

echo "=== 压缩打包构建产物 ==="
zip -r dist.zip dist

echo "=== 复制构建产物到输出目录 ==="
# 复制主应用构建产物
cp ./dist.zip ./output/

echo "=== 构建完成，输出目录内容 ==="
ls -la ./output/

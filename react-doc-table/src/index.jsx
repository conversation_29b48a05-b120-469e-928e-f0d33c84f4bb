import React from 'react';
import ReactDOM from 'react-dom/client';
import { message } from 'antd';
import DocumentManagePage from './components/DocumentManagePage';
import 'antd/dist/reset.css';

// 全局配置 message 组件
message.config({
    getContainer: () => document.querySelector('#react-app-container') || document.body,
    maxCount: 3,
    top: 0,
});

// 定义微应用的生命周期函数
let root = null;

// DocumentTable应用组件
const DocumentTableApp = (props) => {
    // 从props中获取数据和回调函数
    const {
        loading = false,
        items = [],
        total = 0,
        query = {},
        book = {},
        statuses = [],
        documentSlices = [],
        onGetDocuments = () => {},
        onGetDocumentContent = () => {},
        onOpenDrawer = () => {},
        onStatusSwitch = () => {},
        onRemove = () => {},
        onRouteChange = () => {},
        onRefresh = () => {},
        onAddSuccess = () => {},
        addDocumentTrigger = 0,
    } = props;

    return (
        <DocumentManagePage
            loading={loading}
            items={items}
            total={total}
            query={query}
            book={book}
            statuses={statuses}
            documentSlices={documentSlices}
            onGetDocuments={onGetDocuments}
            onGetDocumentContent={onGetDocumentContent}
            onOpenDrawer={onOpenDrawer}
            onStatusSwitch={onStatusSwitch}
            onRemove={onRemove}
            onRouteChange={onRouteChange}
            onRefresh={onRefresh}
            onAddSuccess={onAddSuccess}
            addDocumentTrigger={addDocumentTrigger}
        />
    );
};

/**
 * bootstrap 只会在微应用初始化的时候调用一次
 */
export async function bootstrap() {
    console.log('[react-doc-table] react app bootstraped');
}

/**
 * 应用每次进入都会调用 mount 方法
 */
export async function mount(props) {
    console.log('[react-doc-table] props from main framework', props);
    const { container } = props;
    const containerElement = container
        ? container.querySelector('#react-doc-table-root')
        : document.getElementById('react-doc-table-root');

    if (containerElement) {
        root = ReactDOM.createRoot(containerElement);
        root.render(
            <React.StrictMode>
                <DocumentTableApp {...props} />
            </React.StrictMode>,
        );
    }
}

/**
 * 应用每次 切出/卸载 会调用的方法
 */
export async function unmount(props) {
    console.log('[react-doc-table] react app unmount', props);
    if (root) {
        root.unmount();
        root = null;
    }
}

// 独立运行时的挂载
if (!window.__POWERED_BY_QIANKUN__) {
    const container = document.createElement('div');
    container.id = 'react-doc-table-root';
    document.body.appendChild(container);

    root = ReactDOM.createRoot(container);
    root.render(
        <React.StrictMode>
            React doc 启动了
            <DocumentTableApp />
        </React.StrictMode>,
    );
}



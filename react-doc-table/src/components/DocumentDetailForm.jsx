import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Switch, Space, Upload, Typography, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { dict } from './const';

const { Option, OptGroup } = Select;
const { Text } = Typography;
const { Dragger } = Upload;

const DocumentDetailForm = ({
    documentDetail,
    onSave,
    onCancel,
    loading = false,
    models = [],
    modelsLoading = false,
}) => {
    const [form] = Form.useForm();
    const [isEditing, setIsEditing] = useState(false);
    const [fileList, setFileList] = useState([]);
    const [uploadedFile, setUploadedFile] = useState(null); // 存储新上传的文件信息
    // 初始化表单数据
    useEffect(() => {
        if (documentDetail) {
            const embeddingRule = documentDetail.embeddingRule ? JSON.parse(documentDetail.embeddingRule) : {};
            const formValues = {
                embeddingModelId: documentDetail.embeddingModelId,
            };

            // 根据文档类型设置不同的字段
            if (documentDetail.type === 'virtual') {
                // 虚拟文档：只有标题和模型
                formValues.title = documentDetail.title;
            } else if (documentDetail.type === 'link') {
                // 链接文档：完整字段
                formValues.title = documentDetail.title;
                formValues.sourceUrl = documentDetail.sourceUrl || '';
                formValues.parseId = documentDetail.parseId;
                formValues.delimiter = embeddingRule.delimiter || [];
                formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
                formValues.cronOpen = documentDetail.cronOpen === 1;
                formValues.cronExpression = documentDetail.cronExpression || '';
            } else {
                // 文件类型：不包含标题（来自文件名）
                formValues.parseId = documentDetail.parseId;
                formValues.delimiter = embeddingRule.delimiter || [];
                formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
            }

            form.setFieldsValue(formValues);
        }
    }, [documentDetail, form]);

    useEffect(() => {
        if (documentDetail && documentDetail.bosUrl) {
            setFileList([
                {
                    uid: documentDetail.id,
                    name: documentDetail.title,
                    status: 'done',
                    url: documentDetail.bosUrl,
                    type: documentDetail.type,
                },
            ]);
        } else {
            setFileList([]);
        }
    }, [documentDetail]);

    // 文件上传前验证
    const handleBeforeUpload = (file) => {
        const isLt50M = file.size / 1024 / 1024 < 50;
        if (!isLt50M) {
            message.error('上传文件大小不能超过50MB!');
            return false;
        }

        const allowedTypes = ['doc', 'docx', 'txt', 'pdf', 'json', 'xls', 'xlsx'];
        const fileExt = file.name.split('.').pop().toLowerCase();

        if (!allowedTypes.includes(fileExt)) {
            message.error(`不支持的文件类型，请上传${allowedTypes.join(',')}格式`);
            return false;
        }

        return true;
    };

    const handleFileChange = (info) => {
        let newFileList = [...info.fileList];

        // 文件数量为1个
        newFileList = newFileList.slice(-1);
        setFileList(newFileList);
    };

    // 文件上传处理
    const handleCustomRequest = async ({ file, onSuccess, onError }) => {
        try {
            // 创建FormData
            const formData = new FormData();
            formData.append('files', file);
            formData.append('knowledgeBaseId', documentDetail.knowledgeBaseId);

            // 调用上传API
            const response = await fetch('/rag/api/document/file/upload', {
                method: 'POST',
                body: formData,
                credentials: 'include',
            });

            const result = await response.json();

            if (result.code === 200 && result.data && result.data.length > 0) {
                // 上传成功，保存文件信息
                const uploadedFileInfo = result.data[0];
                setUploadedFile(uploadedFileInfo);

                // 更新文件对象
                file.bosUrl = uploadedFileInfo.bosUrl;
                file.uploadResponse = uploadedFileInfo;

                onSuccess(result);
                message.success(`${file.name} 文件上传成功`);
            } else {
                throw new Error(result.message || '文件上传失败');
            }
        } catch (error) {
            console.error('文件上传失败:', error);
            message.error(`${file.name} 文件上传失败`);
            onError(error);
        }
    };

    // 文件删除处理
    const handleFileRemove = () => {
        if (!isEditing) {
            return false;
        }

        setFileList([]);
        setUploadedFile(null); // 清除上传的文件信息
        return true;
    };

    // 进入编辑模式
    const handleEdit = () => {
        setIsEditing(true);
    };

    // 取消编辑
    const handleCancelEdit = () => {
        setIsEditing(false);
        // 重置表单到原始值
        if (documentDetail) {
            const embeddingRule = documentDetail.embeddingRule ? JSON.parse(documentDetail.embeddingRule) : {};
            const formValues = {
                embeddingModelId: documentDetail.embeddingModelId,
            };

            // 根据文档类型重置不同的字段
            if (documentDetail.type === 'virtual') {
                formValues.title = documentDetail.title;
            } else if (documentDetail.type === 'link') {
                formValues.title = documentDetail.title;
                formValues.sourceUrl = documentDetail.sourceUrl || '';
                formValues.parseId = documentDetail.parseId;
                formValues.delimiter = embeddingRule.delimiter || [];
                formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
                formValues.cronOpen = documentDetail.cronOpen === 1;
                formValues.cronExpression = documentDetail.cronExpression || '';
            } else {
                formValues.parseId = documentDetail.parseId;
                formValues.delimiter = embeddingRule.delimiter || [];
                formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
            }

            form.setFieldsValue(formValues);

            if (documentDetail.bosUrl) {
                setFileList([
                    {
                        uid: documentDetail.id,
                        name: documentDetail.title,
                        status: 'done',
                        url: documentDetail.bosUrl,
                        type: documentDetail.type,
                    },
                ]);
            } else {
                setFileList([]);
            }
        }
        setUploadedFile(null);
        onCancel?.();
    };

    // 保存编辑
    const handleSaveEdit = async () => {
        try {
            const values = await form.validateFields();

            // 构建更新数据
            const updateData = {
                id: documentDetail.id,
                embeddingModelId: values.embeddingModelId,
                // 保持其他字段不变
                type: documentDetail.type,
                knowledgeBaseId: documentDetail.knowledgeBaseId,
                owner: documentDetail.owner,
            };

            if (uploadedFile) {
                updateData.bosUrl = uploadedFile.bosUrl;
                updateData.title = uploadedFile.title;
                updateData.type = uploadedFile.type;
            } else {
                updateData.bosUrl = documentDetail.bosUrl;
            }

            // 根据文档类型添加不同的字段
            if (documentDetail.type === 'virtual') {
                // 虚拟文档：只更新标题和模型
                updateData.title = values.title;
            } else if (documentDetail.type === 'link') {
                // 链接文档：完整字段
                updateData.title = values.title;
                updateData.sourceUrl = values.sourceUrl || '';
                updateData.parseId = values.parseId;
                updateData.embeddingRule = JSON.stringify({
                    delimiter: values.delimiter || [],
                    chunkTokenNum: values.chunkTokenNum || 600,
                });
                updateData.cronOpen = values.cronOpen ? 1 : 0;
                updateData.cronExpression = values.cronExpression || '';
            } else {
                // 文件类型：如果没有新上传文件，保持原标题
                if (!uploadedFile) {
                    updateData.title = documentDetail.title;
                }
                updateData.parseId = values.parseId;
                updateData.embeddingRule = JSON.stringify({
                    delimiter: values.delimiter || [],
                    chunkTokenNum: values.chunkTokenNum || 600,
                });
            }

            await onSave(updateData);
            setIsEditing(false);
            setUploadedFile(null);
        } catch (error) {
            console.error('保存编辑失败:', error);
        }
    };

    if (!documentDetail) {
        return (
            <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">无法获取文档详情</Text>
            </div>
        );
    }

    return (
        <div>
            <div style={{ marginBottom: '24px', textAlign: 'right' }}>
                {!isEditing ? (
                    <Button type="primary" onClick={handleEdit}>
                        编辑
                    </Button>
                ) : (
                    <Space>
                        <Button onClick={handleCancelEdit}>取消</Button>
                        <Button type="primary" loading={loading} onClick={handleSaveEdit}>
                            保存
                        </Button>
                    </Space>
                )}
            </div>

            {/* 编辑表单 */}
            <Form form={form} layout="vertical" disabled={!isEditing}>
                {/* 根据文档类型显示不同的表单字段 */}
                {documentDetail.type === 'virtual' && (
                    <>
                        {/* 虚拟文档：只显示文档标题和选择大模型 */}
                        <Form.Item
                            label="文档标题"
                            name="title"
                            rules={[{ required: true, message: '请输入文档标题' }]}
                        >
                            <Input placeholder="请输入文档标题" />
                        </Form.Item>
                        <Form.Item
                            label="切词模型"
                            name="embeddingModelId"
                            rules={[{ required: true, message: '请选择切词模型' }]}
                        >
                            <Select
                                placeholder="请选择切词模型"
                                loading={modelsLoading}
                                notFoundContent={modelsLoading ? '加载中...' : '暂无数据'}
                            >
                                {models.map((model) => (
                                    <Option key={model.id} value={model.id}>
                                        {model.platform}-{model.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </>
                )}

                {documentDetail.type === 'link' && (
                    <>
                        {/* 链接文档：显示标题、链接、模型、解析、切词、定时任务 */}
                        <Form.Item
                            label="文档标题"
                            name="title"
                            rules={[{ required: true, message: '请输入文档标题' }]}
                        >
                            <Input placeholder="请输入文档标题" />
                        </Form.Item>
                        <Form.Item
                            label="URL 链接"
                            name="sourceUrl"
                            rules={[{ required: true, message: '请输入URL链接' }]}
                        >
                            <Input placeholder="文档的在线访问地址 URL" />
                        </Form.Item>
                        <Form.Item
                            label="切词模型"
                            name="embeddingModelId"
                            rules={[{ required: true, message: '请选择切词模型' }]}
                        >
                            <Select
                                placeholder="请选择切词模型"
                                loading={modelsLoading}
                                notFoundContent={modelsLoading ? '加载中...' : '暂无数据'}
                            >
                                {models.map((model) => (
                                    <Option key={model.id} value={model.id}>
                                        {model.platform}-{model.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="切词类型"
                            name="parseId"
                            rules={[{ required: true, message: '请选择切词类型' }]}
                        >
                            <Select placeholder="请选择切词类型">
                                {dict.parserOptions.map((option) => (
                                    <Option key={option.value} value={option.value}>
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="切片分隔符"
                            name="delimiter"
                            rules={[{ required: true, message: '请指定切词分隔符' }]}
                        >
                            <Select mode="multiple" placeholder="切词分隔符，可以多个，置空表示不做切片">
                                {dict.delimiters.map((group) => (
                                    <OptGroup key={group.label} label={group.label}>
                                        {group.options.map((option) => (
                                            <Option key={option.value} value={option.value}>
                                                {option.label}
                                            </Option>
                                        ))}
                                    </OptGroup>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="最大切片长"
                            name="chunkTokenNum"
                            rules={[{ required: true, message: '请输入最大切片长度' }]}
                        >
                            <Input type="number" placeholder="最大切片长度" />
                        </Form.Item>
                        <Form.Item label="定时任务" name="cronOpen" valuePropName="checked">
                            <Switch />
                        </Form.Item>
                        <Form.Item
                            label="Cron 表达式"
                            name="cronExpression"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(_, value) {
                                        if (getFieldValue('cronOpen') && !value) {
                                            return Promise.reject(new Error('开启定时任务时必须填写Cron表达式'));
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Input placeholder="请输入Cron表达式，例如: 0 0 12 * * ?" />
                        </Form.Item>
                    </>
                )}

                {(documentDetail.type === 'pdf' ||
                    documentDetail.type === 'xlsx' ||
                    documentDetail.type === 'docx' ||
                    documentDetail.type === 'txt' ||
                    documentDetail.type === 'json' ||
                    documentDetail.type === 'excel ') && (
                    <>
                        {/* 文件上传类型：显示选择文件、模型、解析、切词 */}
                        <Form.Item label="选择文件：" required>
                            <Dragger
                                disabled={!isEditing}
                                fileList={fileList}
                                beforeUpload={handleBeforeUpload}
                                customRequest={handleCustomRequest}
                                onChange={handleFileChange}
                                onRemove={handleFileRemove}
                                style={{ marginBottom: 16 }}
                            >
                                <p className="ant-upload-drag-icon">
                                    <UploadOutlined style={{ fontSize: 48, color: isEditing ? '#999' : '#ccc' }} />
                                </p>
                                <p className="ant-upload-text">
                                    {isEditing ? (
                                        <>
                                            将文档拖动至此处，或 <span style={{ color: '#1890ff' }}>点击上传</span>
                                        </>
                                    ) : (
                                        <span style={{ color: '#999' }}>当前文件：{documentDetail.title}</span>
                                    )}
                                </p>
                                {isEditing && (
                                    <p className="ant-upload-hint" style={{ color: '#e6a23c' }}>
                                        单次上传文档数量为5个；单个文件小于50M；支持.doc | .txt | .docx | .pdf | .json |
                                        .excel
                                    </p>
                                )}
                            </Dragger>
                        </Form.Item>
                        <Form.Item
                            label="切词模型"
                            name="embeddingModelId"
                            rules={[{ required: true, message: '请选择切词模型' }]}
                        >
                            <Select
                                placeholder="请选择切词模型"
                                loading={modelsLoading}
                                notFoundContent={modelsLoading ? '加载中...' : '暂无数据'}
                                disabled
                            >
                                {models.map((model) => (
                                    <Option key={model.id} value={model.id}>
                                        {model.platform}-{model.name}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="切词类型"
                            name="parseId"
                            rules={[{ required: true, message: '请选择切词类型' }]}
                        >
                            <Select placeholder="请选择切词类型">
                                {dict.parserOptions.map((option) => (
                                    <Option key={option.value} value={option.value}>
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="切片分隔符"
                            name="delimiter"
                            rules={[{ required: true, message: '请指定切词分隔符' }]}
                        >
                            <Select mode="multiple" placeholder="切词分隔符，可以多个，置空表示不做切片">
                                {dict.delimiters.map((group) => (
                                    <OptGroup key={group.label} label={group.label}>
                                        {group.options.map((option) => (
                                            <Option key={option.value} value={option.value}>
                                                {option.label}
                                            </Option>
                                        ))}
                                    </OptGroup>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="最大切片长"
                            name="chunkTokenNum"
                            rules={[{ required: true, message: '请输入最大切片长度' }]}
                        >
                            <Input type="number" placeholder="最大切片长度" />
                        </Form.Item>
                    </>
                )}
            </Form>
        </div>
    );
};

export default DocumentDetailForm;

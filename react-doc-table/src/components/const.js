// 文档状态常量
export const DOCUMENT_STATUSES = [
	{ key: 1, value: '等待切片' },
	{ key: 2, value: '切片成功' },
	{ key: 3, value: '切片失败' },
	{ key: 4, value: '已被禁用' },
	{ key: 5, value: '切片中' },
];

// 默认知识库信息
export const DEFAULT_BOOK = {
	id: null,
	name: '',
	description: '',
	owner: '',
	createAt: '',
};

// API配置
export const API_CONFIG = {
	headers: {
		'Content-Type': 'application/json',
		originalUrl: window.location.origin,
	},
	withCredentials: true,
};

// 默认分页配置
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_CURRENT_PAGE = 1;

// 默认嵌入模型
export const DEFAULT_EMBEDDING_MODEL = 'qianfan-embedding-v1';

// 默认切片方法
export const DEFAULT_CUT_METHOD = '通用';

// 默认最大Token数
export const DEFAULT_MAX_TOKENS = 600;

// 默认筛选条件
export const DEFAULT_FILTERS = {
	documentId: '',
	documentName: '',
	creator: '',
	status: null,
};

// 默认查询条件
export const DEFAULT_FILTER_QUERY = {
	title: '',
	owner: '',
	status: '',
	id: '',
};

// 状态颜色映射
export const STATUS_COLOR_MAP = {
	1: 'orange', // 等待切片
	2: 'green', // 切片成功
	3: 'red', // 切片失败
	4: 'default', // 已被禁用
	5: 'orange', // 切片中
};

// 默认安全项目数据
export const DEFAULT_SAFE_ITEM = {
	title: '未命名文档',
	type: '未知类型',
	status: 1,
	owner: '未知',
	createAt: '未知时间',
};

// 模态框文本常量
export const MODAL_TEXTS = {
	DELETE_TITLE: '确认删除',
	DELETE_CONTENT_TEMPLATE: (title) => `确定要删除文档 "${title}" 吗？此操作不可恢复。`,
	OK_TEXT: '确定',
	CANCEL_TEXT: '取消',
};

// 消息文本常量
export const MESSAGE_TEXTS = {
	DELETE_SUCCESS: (title) => `文档 "${title}" 删除成功`,
	DELETE_FAILED: (title, error) => `删除文档 "${title}" 失败: ${error}`,
	UPDATE_STATUS_SUCCESS: (title, action) => `文档 "${title}" ${action}成功`,
	UPDATE_STATUS_FAILED: (title, action, error) => `${action}文档 "${title}" 失败: ${error}`,
	DOCUMENT_UPDATE_SUCCESS: '文档更新成功',
	SAVE_FAILED: (error) => `保存失败: ${error}`,
	GET_DOCUMENT_DETAIL_FAILED: (error) => `获取文档详情失败: ${error}`,
	GET_DOCUMENT_LIST_FAILED: (error) => `获取文档数据失败: ${error}`,
	GET_MODEL_LIST_FAILED: (error) => `获取模型列表失败: ${error}`,
	REFRESH_LIST_FAILED: '刷新文档列表失败，请手动刷新页面',
	UPLOAD_SUCCESS: (fileName) => `${fileName} 文件上传成功`,
	UPLOAD_FAILED: (fileName) => `${fileName} 文件上传失败`,
};

// 占位符文本常量
export const PLACEHOLDER_TEXTS = {
	SEARCH_DOCUMENT: '输入文档名称...',
	INPUT_DOCUMENT_ID: '请输入文档ID',
	INPUT_DOCUMENT_NAME: '请输入文档名称',
	INPUT_CREATOR: '请输入创建人',
	SELECT_STATUS: '请选择状态',
};

// 标签文本常量
export const LABEL_TEXTS = {
	DOCUMENT_ID: '文档ID',
	DOCUMENT_NAME: '文档名',
	CREATOR: '创建人',
	STATUS: '状态',
	FILTER_CONDITIONS: '筛选条件',
	RESET: '重置',
	CONFIRM: '确定',
	BACK: '返回',
	ADD_DOCUMENT: '添加文档',
	BASIC_INFO: '基本信息',
	DOCUMENT_TITLE: '文档标题',
	DOCUMENT_TYPE: '文档类型',
	CREATE_TIME: '创建时间',
	UPDATE_TIME: '更新时间',
	PROCESSING_MESSAGE: '处理消息',
	ADDITIONAL_INFO: '附加信息',
	SLICE_INFO: '切片信息',
	OVERVIEW: '概览',
	DETAIL: '详情',
	DISABLE: '禁用',
	ENABLE: '启用',
	DELETE: '删除',
};

// 空状态文本常量
export const EMPTY_TEXTS = {
	NO_DOCUMENTS: '暂无文档数据',
	LOADING: '加载中...',
	NO_DOCUMENT_DETAIL: '无法获取文档详情',
	LOADING_DOCUMENT_DETAIL: '加载文档详情中...',
	NO_SLICE_DATA: '暂无切片数据',
	SELECT_DOCUMENT: '请选择一个文档查看详情',
};
export const dict = {
	delimiters: [
		{
			label: '中文',
			options: [
				{ value: '。', label: '中文句号[。]' },
				{ value: '！', label: '中文感叹符[！]' },
				{ value: '？', label: '中文问号[？]' },
				{ value: '；', label: '中文分号[；]' },
			],
		},
		{
			label: '英文',
			options: [
				{ value: '.', label: '英文句号[.]' },
				{ value: '!', label: '英文感叹号[!]' },
				{ value: '?', label: '英文问号[?]' },
				{ value: ';', label: '英文分号[;]' },
			],
		},
		{
			label: '其他',
			options: [
				{ value: '\n', label: '换行符[\\n]' },
				{ value: '\n\n', label: '换行符*2[\\n\\n]' },
			],
		},
	],
	parserOptions: [
		{ label: '通用', value: 1 },
		{ label: '不切词', value: 2 },
		{ label: 'excel表格', value: 3 },
		{ label: '通用（增强）', value: 4 },
		{ label: 'code index', value: 5 },
	],
};

{"name": "react-doc-table", "version": "1.0.0", "description": "React微应用 - DocumentTable组件", "main": "src/index.tsx", "scripts": {"start": "webpack serve --config webpack.config.js", "build": "webpack --config webpack.config.js", "build:prod": "webpack --config webpack.config.js --mode production", "dev": "webpack serve --config webpack.config.js --mode development"}, "dependencies": {"antd": "^5.12.8", "axios": "^1.7.9", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@types/lodash": "^4.14.202", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "babel-loader": "^10.0.0", "css-loader": "^6.8.1", "html-webpack-plugin": "^5.6.0", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
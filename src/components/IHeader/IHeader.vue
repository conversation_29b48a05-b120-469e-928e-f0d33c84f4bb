<!-- eslint-disable -->
<template>
	<el-row>
		<!-- 面包屑导航 -->
		<el-col :span="14">
			<el-breadcrumb separator="/" class="breadcrumb">
				<el-breadcrumb-item v-for="item in routers" :key="item.path">
					{{ item.meta.title }}
				</el-breadcrumb-item>
			</el-breadcrumb>
		</el-col>

		<!-- 用户信息部分 -->
		<el-col :span="10" class="user">
			<!-- 消息通知 -->
			<el-dropdown trigger="hover" placement="bottom-end" @visible-change="handleDropdownVisible">
				<span class="notification-wrapper">
					<el-badge
						:value="unreadCount"
						:hidden="unreadCount <= 0"
						class="item notification-badge"
						offset="[30, 5]"
					>
						<el-icon><Message class="icon-color" /></el-icon>
					</el-badge>
				</span>
				<template #dropdown>
					<el-dropdown-menu class="custom-dropdown">
						<el-dropdown-item @click="linkToList" class="full-width-item">
							<span class="item-content">消息处理列表</span>
							<span v-if="unreadCount > 0" class="unread-count">({{ unreadCount }}未读)</span>
						</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
			<!-- 用户群链接 -->
			<GroupChat />

			<!-- 用户手册链接 -->
			<el-tooltip content="查看用户手册" placement="bottom-end" effect="light" class="item" trigger="hover">
				<el-link
					style="margin-right: 10px"
					type="primary"
					href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/5eKOIcLZMe/I9qN8S71fAChD1"
					target="_blank"
				>
					<el-icon :size="20"><Reading /></el-icon>
				</el-link>
			</el-tooltip>

			<!-- 用户头像及下拉菜单 -->
			<el-dropdown trigger="hover" placement="bottom-end">
				<el-avatar class="user-icon" :icon="UserFilled" :src="imageUrl" :size="35" />
				<template #dropdown>
					<el-dropdown-menu class="custom-dropdown">
						<el-dropdown-item>{{ username }}（当前用户）</el-dropdown-item>
						<el-dropdown-item @click="linkToUser" class="full-width-item">
							<span class="item-content">个人中心</span>
						</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</el-col>
	</el-row>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import { useMessageStore } from '@/stores/messageStore';
import GroupChat from '@/components/IHeader/GroupChat.vue';

// Store 初始化
const messageStore = useMessageStore();
const userStore = useUserStore();
const { unreadCount } = storeToRefs(messageStore);
const { imageUrl, username } = storeToRefs(userStore);

// 路由相关
const router = useRouter();
const routers = computed(() => {
	return router.currentRoute.value.matched.filter((item) => item.meta.title);
});

// 处理下拉菜单显示/隐藏
const handleDropdownVisible = (visible: boolean) => {
	if (visible) {
		// 下拉显示时立即刷新数据并停止轮询
		messageStore.fetchUnreadCount();
		messageStore.stopPolling();
	} else {
		// 下拉隐藏后重新开始轮询
		messageStore.startPolling();
	}
};

// 导航方法
const linkToUser = () => {
	router.push('/center/user').catch((err) => {
		ElMessage.warning('跳转失败，请稍后重试');
	});
};

const linkToList = () => {
	// 跳转前刷新消息数
	messageStore.fetchUnreadCount();
	router.push('/team/group?id=3').catch((err) => {
		ElMessage.warning('跳转失败，请稍后重试');
	});
};

// 生命周期钩子
onMounted(() => {
	messageStore.fetchUnreadCount();
	messageStore.startPolling();
});

onUnmounted(() => {
	messageStore.stopPolling();
});
</script>

<style scoped>
/* 面包屑样式 */
.breadcrumb {
	line-height: 60px;
	font-size: 15px;
	font-weight: bold;
}

/* 用户部分样式 */
.user {
	line-height: 60px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 15px; /* 增加元素之间的间距 */
}
.notification-wrapper {
	margin-right: 20px;
}
/* 头像样式 */
.user-icon {
	background-color: #525457;
	cursor: pointer; /* 增加鼠标悬浮效果 */
}

/* 下拉菜单样式 */
.custom-dropdown {
	min-width: 150px; /* 设置下拉菜单的最小宽度 */
	padding: 10px;
}

/* el-dropdown 菜单项的样式 */
.el-dropdown-item {
	padding: 8px 12px;
}

/* 确保工具提示不被其他元素遮挡 */
.el-tooltip {
	z-index: 2000;
}

/* 自定义消息徽标样式 */
.notification-badge {
	position: relative;
	display: inline-block;
}

.notification-badge .el-icon {
	font-size: 20px; /* 控制图标的大小 */
	color: #4a4a4a;
}

.notification-badge .el-badge__content {
	position: absolute;
	top: -5px;
	right: -5px;
	font-size: 10px; /* 控制徽标数字的大小 */
	font-weight: bold;
	color: white;
	background-color: #f56c6c; /* 红色背景 */
	border-radius: 50%;
	padding: 3px 6px;
	line-height: 1.2;
}

.icon-color {
	color: #409eff;
}

/* 新增未读计数样式 */
.unread-count {
	margin-left: 8px;
	color: #f56c6c;
	font-size: 12px;
}
</style>

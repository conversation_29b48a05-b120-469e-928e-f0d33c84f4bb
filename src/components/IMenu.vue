<template>
  <div class="menu">
    <el-menu
        text-color="#CFD3DC"
        background-color="000c17"
        @select="handleSelect"
        router
        :default-active="activeIndex"
        :unique-opened="false"
    >
      <div v-for="item in routes" :key="item">
        <el-menu-item :index="item.path" v-if="item.redirect">
          <template #title>
            <el-icon>
              <component :is="item.meta.icon"/>
            </el-icon>
            <span>{{ item.meta.title }}</span></template>
        </el-menu-item>
        <el-sub-menu
            :index="item.path" router
            v-if="isShow(item)"
        >
          <template #title>
            <el-icon>
              <component :is="item.meta.icon"/>
            </el-icon>
            {{ item.meta.title }}
          </template>
          <div v-for="sub in item.children" :key="sub">
            <el-menu-item :index="item.path + '/' + sub.path" v-if="!sub.meta.hidden">
              <template #title>
                <el-icon>
                  <component :is="sub.meta.icon"/>
                </el-icon>
                <span>{{ sub.meta.title }}</span></template>
            </el-menu-item>
          </div>
        </el-sub-menu>
      </div>
    </el-menu>
  </div>
</template>

<script lang="ts">
import {routes} from '@/router/routes.ts';
import { useUserStore } from '@/stores/user';
import { PERMISSION_CONFIG } from '@/utils/permission';

export default {
  data() {
    return {
      activeIndex: '/',
      routes: routes,
      userStore: useUserStore(),
    };
  },
  async mounted() {
    // 初始化时根据当前路由设置激活菜单
    this.updateActiveIndex();
    // 加载用户工作组信息
    if (!this.userStore.groupsLoaded) {
      await this.userStore.fetchUserGroups();
    }
  },
  watch: {
    // 监听路由变化，更新激活菜单
    '$route'() {
      this.updateActiveIndex();
    }
  },
  methods: {
    handleSelect(path: string) {
      localStorage.setItem('currentPath', path);
    },
    updateActiveIndex() {
      const currentPath = this.$route.path;
      // 更新localStorage
      localStorage.setItem('currentPath', currentPath);
      // 更新激活菜单项
      this.activeIndex = currentPath;
    },
    isShow(menu: any) {
      if (!menu.meta.showChildren) {
        return false;
      }

      // 如果是价值模型路由，检查权限
      if (menu.path === '/value-model') {
        const hasPermission = this.userStore.hasGroupPermission(PERMISSION_CONFIG.VALUE_MODEL.groupId);
        return menu.meta.hidden !== true && hasPermission;
      }

      return menu.meta.hidden !== true;
    }
  }
};
</script>

<style scoped>
.el-menu {
  border-right: 1px solid #001529;
}

.el-menu-item {
  background-color: black;

  span {
    padding-left: 5px;
  }
}

.el-menu-item:hover {
  background-color: #3375b9;
  color: #ffffff;
}


</style>
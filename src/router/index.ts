import { createRouter, createWebHashHistory } from 'vue-router';
import { routes } from './routes.ts';
import userApi from '@/api/UserApi.ts';
import { useUserStore } from '@/stores/user';
import { PERMISSION_CONFIG } from '@/utils/permission';

// 存储用户信息的函数
const storeUserInfo = (username: string, imageUrl: string, departmentName: string) => {
	const userStore = useUserStore();

	userStore.updateUserInfo({
		imageUrl: imageUrl,
		username: username,
		departmentName: departmentName,
	});
	sessionStorage.setItem('username', username);
	sessionStorage.setItem('imageUrl', imageUrl);
	sessionStorage.setItem('departmentName', departmentName);
};

// 获取用户信息并处理登录逻辑
const handleLoginInfo = async (next: Function) => {
	try {
		const res = await userApi.getUser();
		if (res.code === 40001) {
			// 如果需要重定向，执行重定向
			window.location = res.redirectUrl;
		} else {
			// 存储用户信息
			storeUserInfo(res.data.name, res.data.hiImageUrl, res.data.departmentName);
			next();
		}
	} catch (error) {
		// 如果捕获错误，使用默认用户信息
		storeUserInfo(
			'guanlin',
			'https://erp.baidu.com/avatar/getAvatar?appCode=ERP&uuap=guanlin&token=QNSAMXOMTN',
			'未知部门',
		);
		console.error(error); // 输出错误信息
		next();
	}
};

const router = createRouter({
	history: createWebHashHistory(),
	routes,
});

router.beforeEach(async (to, from, next) => {
	const username = sessionStorage.getItem('username');

	/// 如果没有用户名，或者用户名是默认的 'undefined'，则需要进行登录处理
	if (!username || username === 'undefined') {
		// 始终尝试调用接口获取用户信息
		handleLoginInfo(next);
	} else {
		// 检查是否访问价值模型相关路由
		if (to.path.startsWith('/value-model')) {
            const userStore = useUserStore();

            // 如果还没有加载工作组信息，先加载
            if (!userStore.groupsLoaded) {
                await userStore.fetchUserGroups();
            }

            // 检查是否有价值模型权限
            if (!userStore.hasGroupPermission(PERMISSION_CONFIG.VALUE_MODEL.groupId)) {
                // 没有权限，重定向到首页
                next('/');
                return;
            }
        }
		next(); // 如果有用户名且权限检查通过，直接放行
	}
});

export default router;

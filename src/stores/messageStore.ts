// src/stores/messageStore.ts
import { defineStore } from 'pinia';
import { ref } from 'vue';
import GroupApi from '@/api/GroupApi';

export const useMessageStore = defineStore('message', () => {
	const unreadCount = ref(0);
	let pollTimer: number | null = null;

	// 获取未读消息数
	const fetchUnreadCount = async () => {
		try {
			const res = await GroupApi.getUnreadMessages();
			unreadCount.value = res?.code === 200 ? res.data : 0;
			return unreadCount.value;
		} catch (error) {
			console.error('获取未读消息数失败', error);
			unreadCount.value = 0;
			return 0;
		}
	};

	// 启动轮询
	const startPolling = (interval = 300000) => {
		stopPolling();
		fetchUnreadCount(); // 立即执行一次
		pollTimer = window.setInterval(fetchUnreadCount, interval);
	};

	// 停止轮询
	const stopPolling = () => {
		if (pollTimer) {
			clearInterval(pollTimer);
			pollTimer = null;
		}
	};

	// 重置未读消息数
	const resetUnreadCount = () => {
		unreadCount.value = 0;
	};

	return {
		unreadCount,
		fetchUnreadCount,
		startPolling,
		stopPolling,
		resetUnreadCount,
	};
});

// stores/user.ts

import { defineStore } from 'pinia';
import groupApi from '@/api/GroupApi';
import { PERMISSION_CONFIG } from '@/utils/permission';

interface UserState {
	imageUrl: string;
	username: string;
	departmentName: string;
	userGroups: any[];
	groupsLoaded: boolean;
}

export const useUserStore = defineStore('user', {
	state: (): UserState => ({
		// 用户信息（从 sessionStorage 初始化）
		imageUrl: sessionStorage.getItem('imageUrl') || '',
		username: sessionStorage.getItem('username') || 'guanlin',
		departmentName: sessionStorage.getItem('departmentName') || '未知部门',
		userGroups: [],
		groupsLoaded: false,
	}),
	actions: {
		// 更新用户头像
		updateImageUrl(newUrl: string) {
			this.imageUrl = newUrl;
			sessionStorage.setItem('imageUrl', newUrl); // 同步到 sessionStorage
		},
		// 更新用户部门
		updateDepartment(newDepartment: string) {
			this.departmentName = newDepartment;
			sessionStorage.setItem('departmentName', newDepartment);
		},
		// 更新用户名
		updateUsername(newName: string) {
			this.username = newName;
			sessionStorage.setItem('username', newName);
		},
		// 批量更新用户信息（可选）
		updateUserInfo(payload: { imageUrl?: string; departmentName?: string; username?: string }) {
			if (payload.imageUrl) {
				this.updateImageUrl(payload.imageUrl);
			}
			if (payload.departmentName) {
				this.updateDepartment(payload.departmentName);
			}
			if (payload.username) {
				this.updateUsername(payload.username);
			}
		},
		// 获取用户工作组信息
		async fetchUserGroups() {
			try {
				const res = await groupApi.getGroup();
				if (res.code === 200) {
					this.userGroups = res.data.items || [];
					this.groupsLoaded = true;
				}
			} catch (error) {
				console.error('获取用户工作组失败:', error);
				this.userGroups = [];
				this.groupsLoaded = true;
			}
		},
		// 检查用户是否在指定工作组中
		hasGroupPermission(groupId: number): boolean {
			return this.userGroups.some(group => group.id === groupId);
		},
		// 检查用户是否有价值模型权限
		hasValueModelPermission(): boolean {
			// 使用导入的权限配置
			return this.hasGroupPermission(PERMISSION_CONFIG.VALUE_MODEL.groupId);
		},
	},
});

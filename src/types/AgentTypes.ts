export interface queryAgentParam {
    type: number,
    keyword: string,
    username: string,
}

// 分页查询参数接口
export interface queryAgentPagingParam {
    type: number;      // 0---查询全部；1--查询 我发布的
    page: number;      // 页码
    size: number;      // 每页数量
    keyword?: string;  // 关键字查询
}

// 分页查询响应数据接口
export interface queryAgentPagingResponseData {
    total: number;
    items: queryAgentResponseData[];
    page: number;
    size: number;
}

export interface queryAgentResponseData {
    id: number;
    name: string;
    description: string;
    groupId: number;
    url: string;
}

export interface saveAgentParam {
    name: string,
    description: string,
    groupId: number,
    knowledgeBaseIds: string,
    recallCount: number,
    similarity: number,
    modelName: string,
    url: string,
    responsePrompter: string
}

export interface updateAgentParam {
    id: number,
    name: string,
    description: string,
    groupId: number,
    knowledgeBaseIds: string,
    recallCount: number,
    similarity: number,
    modelName: string,
    url: string,
    responsePrompt: string
}

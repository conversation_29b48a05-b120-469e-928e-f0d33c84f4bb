import { marked } from 'marked';
import DOMPurify from 'dompurify';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css'; // 选择一种样式

// 配置marked
marked.setOptions({
  breaks: true,
  gfm: true,
  highlight(code: string, lang: string) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (e) {
        console.error('代码高亮错误:', e);
      }
    }
    return hljs.highlightAuto(code).value;
  }
});

// HTML转义辅助函数
/**
 * 将 HTML 字符串中的特殊字符转义为 HTML 实体
 *
 * @param html 要转义的 HTML 字符串
 * @returns 转义后的字符串
 */
function escapeHtml(html: string): string {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

// 安全渲染Markdown
export async function renderMarkdown(content: string): Promise<string> {
  const processedContent = content
    .replace(/\\n/g, '\n')
    .replace(/\\t/g, '\t')
    .replace(/\\"/g, '"')
    .replace(/\\\\/g, '\\');

  const rawHtml = await marked.parse(processedContent);
  return DOMPurify.sanitize(rawHtml);
}

// 安全渲染JSON
export function renderJson(content: string): string {
  let finalContent = content;

  // 去掉可能的Markdown代码块标记
  if (finalContent.startsWith('```')) {
    const endMarkIndex = finalContent.lastIndexOf('```');
    if (endMarkIndex > 3) {
      // 提取```和```之间的内容
      let codeContent = finalContent.substring(3, endMarkIndex);

      // 处理前缀 - 检查并移除json前缀
      if (codeContent.startsWith('json\n')) {
        codeContent = codeContent.substring('json\n'.length);
      } else if (codeContent.startsWith('json')) {
        codeContent = codeContent.substring('json'.length);
      }

      finalContent = codeContent;
    }
  }

  try {
    const parsableJSON = JSON.parse(`"${finalContent}"`);
    const jsonObj = JSON.parse(parsableJSON);
    finalContent = JSON.stringify(jsonObj, null, 2);
    finalContent = finalContent.replace(/\\n/g, '\n');
  } catch (e) {
    console.log('无法解析为JSON，将显示原始数据', e);
    finalContent = finalContent
      .replace(/\\"/g, '"')
      .replace(/\\n/g, '\n')
      .replace(/\\\\/g, '\\');
  }

  return `<pre>${finalContent}</pre>`;
}
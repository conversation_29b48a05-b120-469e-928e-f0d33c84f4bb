class DateUtil {
    format(input: Date): string {
        const year: number = input.getFullYear();
        const month: number = input.getMonth() + 1;
        const day: number = input.getDate()
        let date = {
            year: year,
            month: month < 10 ? '0' + month : month,
            day: day < 10 ? '0' + day : day,
        }
        return date.year + '-' + date.month + '-' + date.day
    }

    getToday(): [string, string] {
        const temp: string = this.format(new Date())
        return [temp + ' 00:00:00', temp + ' 23:59:59'];
    }

    getServenDay(): [string, string] {
        const today = new Date();
        const sevenDaysAgo = new Date(today);
        sevenDaysAgo.setDate(today.getDate() - 7);
        const begin: string = this.format(sevenDaysAgo)
        const end: string = this.format(today)
        return [begin + ' 00:00:00', end + ' 23:59:59'];
    }

    get14Day(): [string, string] {
        const today = new Date();
        const sevenDaysAgo = new Date(today);
        sevenDaysAgo.setDate(today.getDate() - 14);
        const begin: string = this.format(sevenDaysAgo)
        const end: string = this.format(today)
        return [begin + ' 00:00:00', end + ' 23:59:59'];
    }

    get30Day(): [string, string] {
        const today = new Date();
        const sevenDaysAgo = new Date(today);
        sevenDaysAgo.setDate(today.getDate() - 30);
        const begin: string = this.format(sevenDaysAgo)
        const end: string = this.format(today)
        return [begin + ' 00:00:00', end + ' 23:59:59'];
    }

    shortcuts() {
        return [
            {
                text: '今天',
                value: () => {
                    return this.getToday()
                },
            },
            {
                text: '昨天',
                value: (): [string, string] => {
                    const now: Date = new Date();
                    now.setDate(now.getDate() - 1);
                    const temp: string = this.format(now)
                    return [temp + ' 00:00:00', temp + ' 23:59:59'];
                },
            },
            {
                text: '本周',
                value: (): [string, string] => {
                    const now = new Date();
                    const dayOfWeekEnd = this.format(now)
                    now.setDate(now.getDate() - now.getDay() + 1);
                    const dayOfWeekBegin = this.format(now)
                    return [dayOfWeekBegin + ' 00:00:00', dayOfWeekEnd + ' 23:59:59'];
                },
            },
            {
                text: '上周',
                value: (): [string, string] => {
                    const now = new Date();
                    now.setDate(now.getDate() - now.getDay() - 6);
                    const dayOfWeekBegin = this.format(now)
                    now.setDate(now.getDate() + 6)
                    const dayOfWeekEnd = this.format(now)
                    return [dayOfWeekBegin + ' 00:00:00', dayOfWeekEnd + ' 23:59:59'];
                },
            },
            {
                text: '本月',
                value: (): [string, string] => {
                    const now = new Date();
                    const dayOfMonthEnd = this.format(now)
                    now.setDate(1)
                    const dayOfMonthBegin = this.format(now)
                    return [dayOfMonthBegin + ' 00:00:00', dayOfMonthEnd + ' 23:59:59'];
                },
            },
            {
                text: '上月',
                value: (): [string, string] => {
                    let now: Date = new Date();
                    now.setMonth(now.getMonth() - 1);
                    now.setDate(1)
                    const dayOfMonthBegin = this.format(now)
                    now.setDate(new Date(now.getFullYear(), now.getMonth(), -1).getDate())
                    const dayOfMonthEnd = this.format(now)
                    return [dayOfMonthBegin + ' 00:00:00', dayOfMonthEnd + ' 23:59:59'];
                },
            },
            {
                text: '本季度',
                value: (): [string, string] => {
                    const now = new Date();
                    const dayOfQuarterEnd = this.format(now)
                    const firstMonthOfQuarter = Math.floor(now.getMonth() / 3) * 3;
                    now.setMonth(firstMonthOfQuarter);
                    now.setDate(1)
                    const dayOfQuarterBegin = this.format(now)
                    return [dayOfQuarterBegin + ' 00:00:00', dayOfQuarterEnd + ' 23:59:59'];
                },
            },
            {
                text: '上季度',
                value: (): [string, string] => {
                    const now = new Date();
                    const firstMonthOfQuarter = Math.floor(now.getMonth() / 3) * 3;
                    now.setMonth(firstMonthOfQuarter - 3);
                    now.setDate(1)
                    const dayOfQuarterBegin = this.format(now)
                    now.setMonth(now.getMonth() + 2);
                    now.setDate(new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate())
                    const dayOfQuarterEnd = this.format(now)
                    return [dayOfQuarterBegin + ' 00:00:00', dayOfQuarterEnd + ' 23:59:59'];
                },
            },
            {
                text: '今年',
                value: () => {
                    const now = new Date();
                    const dayOfYearEnd = this.format(now)
                    now.setMonth(0)
                    now.setDate(1)
                    const dayOfYearBegin = this.format(now)
                    return [dayOfYearBegin + ' 00:00:00', dayOfYearEnd + ' 23:59:59'];
                },
            },
            {
                text: '去年',
                value: () => {
                    const now = new Date();
                    now.setFullYear(now.getFullYear() - 1);
                    now.setMonth(0)
                    now.setDate(1)
                    const dayOfYearBegin = this.format(now)
                    now.setMonth(11)
                    now.setDate(new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate())
                    const dayOfYearEnd = this.format(now)
                    return [dayOfYearBegin + ' 00:00:00', dayOfYearEnd + ' 23:59:59'];
                },
            }
        ]
    }
}

const dateUtil: DateUtil = new DateUtil()
export default dateUtil
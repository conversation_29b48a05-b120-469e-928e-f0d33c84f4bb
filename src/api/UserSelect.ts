import http from "@/utils/http.ts";


const BASEURL = "/rag/api";
// 枚举请求接口地址

enum API {
    QUERY_CURRENT_USER_URL = BASEURL + '/user/query',
}

class UserSlect {
    getUser(body: object) {
        return http.post(API.QUERY_CURRENT_USER_URL, body, {
            headers: {
                // 'Content-Type': 'applijson'
            }
        }
        )
    }
}

const userSlect: UserSlect = new UserSlect()
export default userSlect

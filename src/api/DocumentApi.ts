import http from "@/utils/http.ts";
import userApi from "@/api/UserApi.ts";


const BASEURL = "/rag/api";
// 枚举请求接口地址
enum API {
    QUERY_BY_ID = BASEURL + '/document/query/content',
    UPLOAD_FILE = BASEURL + '/document/file/upload',
    PAGING_URL = BASEURL + '/document/paging',
    DELETE_URL = BASEURL + '/document/delete',
    UPDATE_URL = BASEURL + '/document/update',
    UPDATE_STATE_URL = BASEURL + '/document/status/update',
    ADD_URL = BASEURL + '/document/add',
}

class DocumentApi {

    getDocumentContent(id) {
        const body = {
            documentId: id,
            type: 0
        }
        return http.post(API.QUERY_BY_ID, body)
    }

    upload(body) {
        return http.post(API.UPLOAD_FILE, body, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }

    /**
     * 获取列表
     */
    getDocuments(query) {
        return http.post(API.PAGING_URL, query)
    }

    /**
     * 删除
     */
    remove(body) {
        body['owner'] = userApi.loginUser();
        return http.post(API.DELETE_URL, body)
    }

    statusSwitch(body) {
        body['owner'] = userApi.loginUser();
        return http.put(API.UPDATE_STATE_URL, body)
    }

    edit(document) {
        return http.put(API.UPDATE_URL, document)
    }

    create(document) {
        return http.post(API.ADD_URL, document)
    }
}

const documentApi: DocumentApi = new DocumentApi()
export default documentApi

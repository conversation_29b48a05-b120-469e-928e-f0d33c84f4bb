import http from '@/utils/http.ts';

const BASEURL = '/rag/api';
// 枚举请求接口地址

enum API {
	QUERY_CURRENT_USER_URL = BASEURL + '/user/current',
}

class UserApi {
	getUser() {
		return http.get(API.QUERY_CURRENT_USER_URL);
	}

	loginUser() {
		return sessionStorage.getItem('username');
	}

	isAdmin(admins: string) {
		const userName = this.loginUser();
		if (userName == null) {
			return false;
		}
		return admins?.includes(userName!);
	}
}

const userApi: UserApi = new UserApi();
export default userApi;

import http from "@/utils/http.ts";
import type {queryAgentParam, queryAgentPagingParam, saveAgentParam, updateAgentParam} from "@/types/AgentTypes.ts";

const BASEURL = "/rag/api";

// 枚举请求接口地址
enum API {
    QUERY_URL= BASEURL + '/agent/query',
    QUERY_PAGING_URL= BASEURL + '/agent/query/paging',
    SAVE_URL= BASEURL + '/agent/add',
    UPDATE_URL= BASEURL + '/agent/update',
    QUERY_BY_ID_URL= BASEURL + '/agent/detail',
}

class AgentApi {
    // 旧的不分页
    getAgent = (data: queryAgentParam) =>  http.post(API.QUERY_URL, data);
    // 新的分页查询
    getAgentPaging = (data: queryAgentPagingParam) => http.post(API.QUERY_PAGING_URL, data);
    saveAgent = (data: saveAgentParam) =>  http.post(API.SAVE_URL, data);
    getAgentById = (id: number) => http.get(`${API.QUERY_BY_ID_URL}?id=${id}`);
    updateAgent = (data: updateAgentParam) =>  http.put(API.UPDATE_URL, data);
}

const agentApi: AgentApi = new AgentApi()
export default agentApi

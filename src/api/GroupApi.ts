import http from '@/utils/http.ts';

const BASEURL = '/rag/api';
// 枚举请求接口地址
enum API {
	// 查询所有工作组信息
	QUERY_ALL_GROUP = BASEURL + '/workgroup/all/query',
	// 查询当前用户工作组加入情况
	QUERY_GROUP_JOIN = BASEURL + '/workgroup/join/query',
	// 更新工作组信息
	UPDATE_GROUP_INFO = BASEURL + '/workgroup/update',
	// 更新工作组用户信息
	UPDATE_GROUP_INFO_USER = BASEURL + '/workgroup/update',
	// 当前团队详情展示
	DETAIL_GROUP = BASEURL + '/workgroup/query',
	// 创建工作组
	ADD_GROUP = BASEURL + '/workgroup/add',
	// 加入工作组
	JOIN_GROUP = BASEURL + '/workgroup/apply/join',
	// 查询工作组列表
	LIST_GROUP = BASEURL + '/audit/message/query',
	// 审批工作组-yes
	APPROVE_GROUP = BASEURL + '/workgroup/apply/approve',
	// 审批工作组-no
	REJECTY_GROUP = BASEURL + '/workgroup/apply/reject',
	// 工作组消息提示
	MEASSAGE_TIPS = BASEURL + '/audit/message/num/query',
	// 查询工作组用户列表
	BU_LIST = BASEURL + '/bu/query',
	// 查询已加入的工作组列表
	QUERY_JOINED_LIST = BASEURL + '/workgroup/query/joined/list',
	// 查询所有工作组列表（无参数）
	QUERY_ALL_GROUP_LIST = BASEURL + '/workgroup/query/all/list',
}

// 定义工作组接口
export interface Workgroup {
	id: number;
	name: string;
	business: string;
	createAt?: string;
	createUser?: string;
	manager?: string;
	token?: string;
	updateAt?: string;
}

class GroupApi {
	/**
	 * 获取用户所在的组信息
	 *
	 * @returns 返回用户所在组的信息
	 */
	getGroup(
		params: {
			page?: number;
			pageSize?: number;
		} = {},
	) {
		return http.post(API.QUERY_GROUP_JOIN, params);
	}

	/**
	 * 获取所有群组信息
	 *
	 * @returns 返回所有群组的查询结果
	 */
	getGroups(data: object = {}) {
		return http.post(API.QUERY_ALL_GROUP, data);
	}

	/**
	 * 编辑工作组的用户信息
	 *
	 * @param data 包含要更新的工作组信息和用户信息的数据对象
	 * @param data.workGroup 工作组信息对象
	 * @param data.workGroup.id 工作组ID
	 * @param data.workGroup.name 工作组名称
	 * @param data.workGroup.business 工作组业务类型
	 * @param data.workGroup.manager 工作组负责人
	 * @param data.workGroup.createUser 工作组创建人
	 * @param data.role 用户角色ID
	 * @param data.users 用户ID列表，以逗号分隔
	 * @returns 返回一个Promise对象，表示HTTP PUT请求的异步结果
	 */
	editGroupUser(data: {
		workGroup: {
			id: number;
			name: string;
			business: string;
			manager: string;
			createUser: string;
		};
		role: number;
		users: string;
	}) {
		return http.put(API.UPDATE_GROUP_INFO, data);
	}

	/**
	 * 编辑群组信息
	 *
	 * @param data 包含群组信息的对象
	 * @param data.name 群组名称
	 * @param data.business 业务类型
	 * @param data.manager 群组管理员
	 * @param data.createUser 创建者
	 * @returns 返回更新后的群组信息
	 */
	editGroup(data: { name: string; business: string; manager: string; createUser: string }) {
		return http.post(API.UPDATE_GROUP_INFO, data);
	}

	/**
	 * 创建工作组
	 *
	 * @param data 包含工作组信息的对象
	 * @param data.name 工作组名称
	 * @param data.business 所属事业群
	 * @param data.manager 经理
	 * @param data.createUser 工作组创建者
	 * @returns 返回创建工作组的POST请求结果
	 */
	createGroup(data: { name: string; business: string; manager: string; createUser: string }) {
		// 发送POST请求，将书籍信息添加到服务器
		return http.post(API.ADD_GROUP, data);
	}

	/**
	 * 提交申请
	 *
	 * @param data 包含申请信息的对象
	 * @param data.workGroupId 工作组ID
	 * @param data.role 角色
	 * @param data.auditor 审核人
	 * @param data.reason 理由（可选）
	 * @returns 返回Axios的响应对象
	 */
	submitApplication(data: { workGroupId: number; role: number; auditor: string; reason?: string }) {
		return http.post(API.JOIN_GROUP, data);
	}

	/**
	 * 检查列表
	 *
	 * @param data 请求参数对象
	 * @returns 返回检查列表的结果
	 */
	checkList(data: {}) {
		return http.get(API.LIST_GROUP, data);
	}
	/**
	 * 发送同意请求
	 *
	 * @param data 请求体数据，类型为对象
	 * @returns 返回一个Promise，解析为http.post的响应结果
	 */
	applyYes(data: {}) {
		return http.post(API.APPROVE_GROUP, data);
	}
	/**
	 * 发送拒绝入群请求
	 *
	 * @param data 请求数据
	 * @returns 发送请求的结果
	 */
	applyNo(data: {}) {
		return http.post(API.REJECTY_GROUP, data);
	}

	/**
	 * 获取未读消息数量
	 *
	 * @returns 返回未读消息数量
	 */
	getUnreadMessages() {
		return http.get(API.MEASSAGE_TIPS);
	}

	/**
	 * 获取业务单元列表
	 *
	 * @returns 返回业务单元列表的Promise对象
	 */
	getBuList() {
		return http.get(API.BU_LIST);
	}

	/**
	 * 获取用户已加入的工作组列表
	 *
	 * @returns 返回用户已加入的工作组列表
	 */
	getJoinedList() {
		return http.post(API.QUERY_JOINED_LIST);
	}

	/**
	 * 获取所有工作组列表（无参数）
	 *
	 * @returns 返回所有工作组列表
	 */
	getAllGroupsList() {
		// 发送HTTP POST请求获取所有群组列表
		return http.post(API.QUERY_ALL_GROUP_LIST);
	}
}

const groupApi: GroupApi = new GroupApi();
export default groupApi;

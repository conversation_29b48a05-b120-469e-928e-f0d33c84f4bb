<!-- eslint-disable -->
<template>
  <div class="stream">
    <el-avatar v-if="isUser" :size="32" color="blue" icon="UserFilled" />
    <img v-else src="@/assets/chat.webp" height="32px" width="32px" />
    <!--bca-disable-->
    <div class="message-container">
      <div
        :class="['message-content', isUser ? 'ask-css' : 'response-css']"
        v-html="content"
      ></div>
      <!-- 添加footer插槽，用于放置参考文档 先不用了 有缘再用-->
      <!-- <slot name="footer"></slot> -->
    </div>
  </div>
</template>

<script lang="ts">
/*  eslint-disable */
import { defineComponent } from "vue";
export default defineComponent({
  name: "ChatMessage",
  props: {
    isUser: Boolean,
    content: String,
  },
});
</script>

<style scoped>
.stream {
  margin-left: 10px;
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  gap: 10px;
  margin-bottom: 12px; /* 调整消息间距 */
  overflow-y: auto;
}

.stream img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  margin-top: 4px; /* 头像与第一行文字对齐 */
}

.message-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.message-content {
  word-break: break-word;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5; /* 调整行高 */
  padding: 12px 16px;
  border-radius: 12px;
}

.ask-css {
  color: #fff;
  background: #4b87ff;
}

.response-css {
  background: white;
  color: #333;
}

/* 调整Markdown渲染后的样式 */
.message-content :deep() p {
  margin: 0.5em 0; /* 调整段落间距 */
}

.message-content :deep() pre {
  margin: 0.5em 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow-x: auto;
}

.message-content :deep() code {
  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
  font-size: 85%;
  background-color: rgba(175, 184, 193, 0.2);
  border-radius: 3px;
  padding: 0.2em 0.4em;
}

.message-content :deep() pre code {
  background-color: transparent;
  padding: 0;
}
</style>

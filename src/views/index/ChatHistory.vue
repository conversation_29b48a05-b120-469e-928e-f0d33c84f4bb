<template>
	<div class="conversation-item">
		<ChatMessage :isUser="true" :content="conversation.question" />
		<ChatMessage :isUser="false" :content="conversation.answer" />
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import ChatMessage from '@/views/index/ChatMessage.vue';

export default defineComponent({
	name: 'ChatHistory',
	components: { ChatMessage },
	props: {
		conversation: {
			type: Object as () => { question: string; answer: string; docLinks?: Array<{ id: string; fileName: string }> },
			required: true,
		},
	},
});
</script>

<style scoped>
.conversation-item {
	margin-bottom: 20px;
	animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}
</style>

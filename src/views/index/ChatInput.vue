<template>
	<div class="input-container">
		<el-row justify="center" style="margin-top: 20px">
			<el-col :span="22" style="margin-left: 10px; border-radius: 12px">
				<el-card :body-style="{ padding: '0px' }" class="fixed-input">
					<div
						class="grid-content bg-purple-dark"
						style="margin: 10px; display: flex; justify-content: flex-end; align-items: center"
					>
						<el-select
							v-model="selectedValue"
							placeholder="Agent"
							size="large"
							style="width: 150px"
							:disabled="isLoading"
						>
							<el-option
								v-for="item in options"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
						<el-input
							v-model="prompt"
							placeholder="输入你的问题..."
							style="
								height: 40px;
								border: none !important;
								flex: 1;
								margin-left: 10px;
								margin-right: 10px;
							"
							@keyup.enter="onSubmit"
							:disabled="isLoading"
						></el-input>
						<el-button
							link
							@click="onSubmit"
							style="width: 40px"
							:loading="isLoading"
							:disabled="!prompt.trim() || isLoading"
						>
							<img src="@/assets/play.png" height="25px" width="25px" v-if="!isLoading" />
						</el-button>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { ElMessage } from 'element-plus';

// 定义本地存储的key
const AGENT_SELECTION_STORAGE_KEY = 'lastSelectedAgent';

export default defineComponent({
	name: 'ChatInput',
	props: {
		isLoading: Boolean,
		options: {
			type: Array as () => Array<{ value: string; label: string }>,
			default: () => [],
		},
		modelValue: String,
	},
	emits: ['update:modelValue', 'submit'],
	data() {
		return {
			selectedValue: this.getLastSelectedAgent(), // 初始化时从本地存储读取
			isOptionsLoaded: false, // 添加加载状态标志
		};
	},
	computed: {
		prompt: {
			get() {
				return this.modelValue;
			},
			set(value) {
				this.$emit('update:modelValue', value);
			},
		},
	},
	watch: {
		// 监听options变化
		options: {
			immediate: true, // 立即执行一次
			handler(newOptions) {
				if (newOptions.length > 0 && !this.isOptionsLoaded) {
					this.isOptionsLoaded = true;
					this.selectedValue = this.getLastSelectedAgent();
				}

				// 检查当前选择是否有效
				if (this.selectedValue && !newOptions.some((opt) => opt.value === this.selectedValue)) {
					this.selectedValue = '';
				}
			},
		},
		// 监听selectedValue变化
		selectedValue(newVal) {
			if (newVal) {
				localStorage.setItem(AGENT_SELECTION_STORAGE_KEY, newVal);
			} else {
				localStorage.removeItem(AGENT_SELECTION_STORAGE_KEY);
			}
		},
	},
	methods: {
		onSubmit() {
			if (this.selectedValue && this.prompt.trim()) {
				this.$emit('submit', {
					prompt: this.prompt,
					agentId: this.selectedValue,
				});
			} else {
				ElMessage.error('请选择Agent并输入问题');
			}
		},
		// 从本地存储获取上次选择的Agent
		// 修改后的获取上次选择的方法
		getLastSelectedAgent(): string {
			try {
				// 尝试从localStorage获取上次选择的Agent
				const lastSelected = localStorage.getItem(AGENT_SELECTION_STORAGE_KEY);
				// 如果没有存储的值或options未加载，直接返回空
				if (!lastSelected || !this.options.length) {
					console.log('No stored value or options not loaded');
					return '';
				}

				// 查找匹配的选项
				const matchedOption = this.options.find((opt) => String(opt.value) === String(lastSelected));

				if (matchedOption) {
					return matchedOption.value; // 返回匹配的value
				}

				console.log('No matching option found');
				return '';
			} catch (err) {
				return '';
				console.error('Error retrieving last selected agent:', err);
			}
		},
	},
});
</script>

<style scoped>
.input-container {
	position: absolute;
	bottom: 10px;
	left: 0;
	right: 0;
	padding: 0 24px 20px;
	background: linear-gradient(to top, rgba(243, 246, 249, 1), rgba(243, 246, 249, 0));
	z-index: 100;
}

.fixed-input {
	width: 100%;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}
</style>

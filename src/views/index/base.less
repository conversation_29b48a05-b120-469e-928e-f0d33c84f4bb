.demo-1 {
  font-family: "Franklin Gothic Medium", "Helvetica Neue", Helvetica, Arial, sans-serif;
  width: 100%;
  height: calc(100vh - 210px);
  overflow: hidden;
  box-sizing: border-box;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.demo-1 main {
  position: relative;
  width: 100%;
  height: 100%;
}

.content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  height: 100%;
}

/* Slideshow */
.slideshow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-content: center;
  justify-content: center;
  pointer-events: auto;
  z-index: 100;
  opacity: 1;
}

.slide__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  //background-color: #fff;
}

/* Word + SVG styles */
.word {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin: 0 0 0.25em 0;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  grid-area: title;
  font-size: 9vw;
  color: #27768d;
  letter-spacing: 10px;
  font-weight: bold;
}

.word span {
  display: block;
  position: relative;
  flex: none;
  white-space: pre;
}

/deep/.shapes {
  position: absolute;
  top: 0;
  left: 0;
}

@media screen and (max-width: 40em) {
  .slide .word {
    font-size: 2em;
  }
}

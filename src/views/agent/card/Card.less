.image {
  width: 100%;
  height: 150px;
  display: block;
  cursor: pointer;
}

@media screen and (max-width: 1150px) {
  .detail .id-and-owner > div:last-child {
    display: none;
  }
}

.card {
  margin-top: 10px;
  position: relative;

  .edit-btn {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

.titles {
  padding-top: 10px;

  > div {
    color: #303133;
    font-size: 20px;
    box-sizing: border-box;
    padding: 0 12px;
    display: flex;
    word-break: break-all;

    .title {
      display: inline-block;
      width: 55px;
      text-align: right;
      color: #606266;
      flex-shrink: 0;
    }

    > span {
      &:last-child {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &.summary {
        height: 40px;
        cursor: pointer;
        white-space: initial;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

  .id-and-owner {
    justify-content: space-between;
    overflow: hidden;

    > div {
      &:first-child {
        flex-shrink: 0;
      }

      &:last-child {
        flex-grow: 1;
        text-align: right;
        margin-left: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.detail {
  padding-top: 5px;

  > div {
    color: rgb(132, 134, 140);
    font-size: 14px;
    box-sizing: border-box;
    padding: 0 12px;
    display: flex;
    word-break: break-all;

    .title {
      display: inline-block;
      width: 55px;
      text-align: right;
      color: #606266;
      flex-shrink: 0;
    }

    > span {
      &:last-child {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &.summary {
        height: 40px;
        cursor: pointer;
        white-space: initial;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

  .id-and-owner {
    justify-content: space-between;
    overflow: hidden;

    > div {
      &:first-child {
        flex-shrink: 0;
      }

      &:last-child {
        flex-grow: 1;
        text-align: right;
        margin-left: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

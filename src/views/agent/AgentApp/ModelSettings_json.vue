<!-- eslint-disable -->
<template>
	<div class="model-settings">
		<el-card class="settings-card">
			<template #header>
				<div class="section-header">
					<span class="group-title">模型选择</span>
					<el-button type="primary" text :icon="Edit" class="action-button" @click="openPromptDialog">
						用户指令
					</el-button>
				</div>
			</template>

			<el-row class="model-selection">
				<el-col :span="6">
					<span class="selection-label">生成模型</span>
				</el-col>
				<el-col :span="16" :offset="2">
					<el-select v-model="selectedModel" placeholder="选择模型" size="large" class="model-selector">
						<el-option
							v-for="item in validatedModelOptions"
							:key="item.id"
							:label="`${item.platform}-${item.name}`"
							:value="item.id"
						/>
					</el-select>
				</el-col>
			</el-row>
		</el-card>

		<el-dialog v-model="dialogVisible" title="模型交互设置" width="70%" @closed="handleDialogClose">
			<el-row class="model-selection">
				<el-col :span="4">
					<span class="selection-label">用户角色</span>
				</el-col>
				<el-col :span="20">
					<el-input v-model="localRolePrompt" placeholder="请输入用户角色名" @input="updateRolePrompt" />
				</el-col>
			</el-row>

			<json-editor-panel
				title="用户指令(JSON)"
				v-model="ruleEditorData"
				:editor-key="'rule-' + editorKey"
				@error-change="(msg) => (ruleJsonError = msg)"
			/>

			<json-editor-panel
				title="应用回复(JSON)"
				v-model="responseEditorData"
				:editor-key="'response-editor-' + editorKey"
				@error-change="(hasError) => (responseJsonError = hasError)"
			/>

			<template #footer>
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="savePrompts" :disabled="hasJsonErrors || !isValidJsonData">
					保存
				</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue';
import { Edit } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import _ from 'lodash';
import JsonEditorPanel from './JsonEditorPanel.vue';

const parseJsonSafely = (jsonString) => {
	try {
		return JSON.parse(jsonString || '{}');
	} catch (e) {
		console.error('JSON解析错误:', e);
		return {};
	}
};

export default {
	name: 'ModelSettings',
	components: {
		JsonEditorPanel,
	},
	props: {
		modelOptions: {
			type: Array,
			default: () => [],
			validator: (value) => value.every((item) => item.id && item.platform && item.name),
		},
		modelId: {
			type: String,
			required: true,
			default: '',
		},
		rolePrompt: {
			type: String,
			default: '',
		},
		rulePrompt: {
			type: String,
			default: '{}',
			validator: (value) => {
				try {
					JSON.parse(value);
					return true;
				} catch {
					return false;
				}
			},
		},
		responsePrompt: {
			type: String,
			default: '{}',
			validator: (value) => {
				try {
					JSON.parse(value);
					return true;
				} catch {
					return false;
				}
			},
		},
	},
	emits: {
		'update:modelId': (value) => typeof value === 'string',
		'update:rolePrompt': (value) => typeof value === 'string',
		'update:modelSettings': (value) =>
			typeof value.rolePrompt === 'string' &&
			typeof value.rulePrompt === 'string' &&
			typeof value.responsePrompt === 'string',
	},
	setup(props, { emit }) {
		// State
		const dialogVisible = ref(false);
		const editorKey = ref(0);
		const ruleEditorData = ref(parseJsonSafely(props.rulePrompt));
		const responseEditorData = ref(parseJsonSafely(props.responsePrompt));
		const ruleJsonError = ref('');
		const responseJsonError = ref('');
		const localRolePrompt = ref(props.rolePrompt);

		// Computed
		const selectedModel = computed({
			get: () => props.modelId,
			set: (val) => emit('update:modelId', val),
		});

		const validatedModelOptions = computed(() => {
			return props.modelOptions.filter((item) => item.id);
		});

		const isValidJsonData = computed(() => {
			return (
				ruleEditorData.value &&
				responseEditorData.value &&
				!_.isEmpty(ruleEditorData.value) &&
				!_.isEmpty(responseEditorData.value)
			);
		});

		// Watchers
		watch(
			() => props.rulePrompt,
			(newVal) => {
				ruleEditorData.value = parseJsonSafely(newVal);
			}
		);

		watch(
			() => props.responsePrompt,
			(newVal) => {
				responseEditorData.value = parseJsonSafely(newVal);
			}
		);

		watch(
			() => props.rolePrompt,
			(newVal) => {
				localRolePrompt.value = newVal;
			}
		);

		const hasJsonErrors = computed(() => {
			const hasError = Boolean(ruleJsonError.value) || Boolean(responseJsonError.value);
			return hasError;
		});
		// Methods
		const updateRolePrompt = (value) => {
			emit('update:rolePrompt', value);
		};

		const openPromptDialog = async () => {
			editorKey.value += 1;
			await nextTick();
			dialogVisible.value = true;
		};

		const handleDialogClose = () => {
			ruleEditorData.value = parseJsonSafely(props.rulePrompt);
			responseEditorData.value = parseJsonSafely(props.responsePrompt);
			ruleJsonError.value = '';
			responseJsonError.value = '';
		};

		const handleRuleChange = (value) => {
			ruleEditorData.value = value;
		};

		const handleResponseChange = (value) => {
			responseEditorData.value = value;
		};

		const savePrompts = () => {
			if (localRolePrompt.value.trim() === '') {
				ElMessage.error('角色提示不能为空');
				return;
			}

			if (hasJsonErrors.value) {
				ElMessage.error('请先修正JSON格式错误');
				return;
			}

			emit('update:modelSettings', {
				rolePrompt: localRolePrompt.value,
				rulePrompt: JSON.stringify(ruleEditorData.value, null, 2),
				responsePrompt: JSON.stringify(responseEditorData.value, null, 2),
			});

			ElMessage.success('设置保存成功');
			dialogVisible.value = false;
		};

		return {
			Edit,
			selectedModel,
			validatedModelOptions,
			dialogVisible,
			localRolePrompt,
			ruleEditorData,
			responseEditorData,
			ruleJsonError,
			responseJsonError,
			hasJsonErrors,
			isValidJsonData,
			editorKey,
			updateRolePrompt,
			openPromptDialog,
			handleDialogClose,
			handleRuleChange,
			handleResponseChange,
			savePrompts,
		};
	},
};
</script>

<style lang="less" scoped>
.model-settings {
	border-radius: 8px;
}
.group-title {
	font-size: 14px;
	color: var(--el-text-color-secondary);
	margin: 5px;
	font-weight: 500;
}
.settings-card {
	padding: 10px;
	border-radius: 8px;
}

.section-header {
	display: flex;
	align-items: center;
	.action-button {
		margin-left: auto;
		font-size: 13px;
	}
}

.model-selection {
	margin: 10px 0;
	display: flex;
	align-items: center;
}

.selection-label {
	font-size: 14px;
	color: var(--el-text-color-secondary);
}

.model-selector {
	width: 100%;
}
</style>

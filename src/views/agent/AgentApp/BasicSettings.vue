<!-- eslint-disable -->
<template>
	<div class="basic-settings">
		<el-card style="padding: 10px">
			<template #header>
				<div class="section-header">
					<span class="group-title">基本信息</span>
				</div>
			</template>
			<el-row>
				<el-col>
					<div class="settings-content">
						<el-row>
							<el-col :span="8">
								<el-upload class="avatar-uploader" :action="baseURL" :show-file-list="false"
									:on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
									<img v-if="imageUrl" :src="imageUrl" class="avatar" />
									<el-icon v-else class="avatar-uploader-icon">
										<Plus />
									</el-icon>
								</el-upload>
							</el-col>

							<el-col :span="16">
								<div>
									<el-input v-model="computedAgentName" placeholder="我的应用名" />
									<el-input v-model="computedDescription" :rows="2" class="description-input"
										type="textarea" placeholder="应用描述" />
								</div>
							</el-col>
						</el-row>

						<el-tag type="success" class="image-tip">图片的长宽比为360*140为最优，其他的会被拉伸哦</el-tag>

						<el-row class="owner-row">
							<el-col :span="8">
								<span class="label">工作组</span>
							</el-col>
							<el-col :span="16">
								<el-select v-model="selectedGroupId" placeholder="请选择工作组" clearable
									loading-text="加载中..." :loading="workgroupsLoading" @change="handleGroupChange"
									style="width: 100%">
									<el-option v-for="item in displayWorkgroups" :key="item.id"
										:label="`${item.id} - ${item.name} (${item.business})`" :value="item.id" />
								</el-select>
							</el-col>
						</el-row>
					</div>
				</el-col>
			</el-row>
		</el-card>
	</div>
</template>

<script>
import groupApi from '@/api/GroupApi';
import { ElMessage } from 'element-plus';

export default {
	props: {
		baseURL: String,
		imageUrl: String,
		agentName: String,
		description: String,
		groupId: Number,
	},
	emits: ['update:imageUrl', 'update:agentName', 'update:description', 'update:groupId'],
	data() {
		return {
			workgroups: [], // 用户加入的工作组
			allWorkgroups: [], // 所有工作组
			workgroupsLoading: false,
			allWorkgroupsLoading: false,
			selectedGroupId: null,
		};
	},
	watch: {
		groupId: {
			immediate: true,
			handler(newVal) {
				if (newVal) {
					this.selectedGroupId = typeof newVal === 'string' ? parseInt(newVal, 10) : newVal;
					console.log('Updated selectedGroupId from prop:', this.selectedGroupId);
				}
			}
		}
	},
	computed: {
		computedImageUrl: {
			get() {
				return this.imageUrl;
			},
			set(value) {
				this.$emit('update:imageUrl', value);
			},
		},
		computedAgentName: {
			get() {
				return this.agentName;
			},
			set(value) {
				this.$emit('update:agentName', value);
			},
		},
		computedDescription: {
			get() {
				return this.description;
			},
			set(value) {
				this.$emit('update:description', value);
			},
		},
		// 如果查看别人发布的智能体，可能工作组不是自己加入的，所以需要从所有工作组中查找，展示name
		displayWorkgroups() {
			// 如果没有选中的工作组，直接返回用户的工作组
			if (!this.selectedGroupId) {
				return this.workgroups;
			}

			// 检查选中的工作组是否已经在用户的工作组列表中
			const userGroupIds = this.workgroups.map(g => g.id);
			const isSelectedInUserGroups = userGroupIds.includes(this.selectedGroupId);

			// 如果已经在用户工作组列表中，直接返回用户工作组
			if (isSelectedInUserGroups) {
				return this.workgroups;
			}

			// 如果不在用户工作组列表中，查找该工作组的详细信息
			const selectedGroup = this.allWorkgroups.find(g => g.id === this.selectedGroupId);
			if (!selectedGroup) {
				return this.workgroups; // 如果找不到，返回用户工作组
			}

			// 将该工作组添加到用户工作组列表中返回
			return [...this.workgroups, selectedGroup];
		}
	},
	methods: {
		handleAvatarSuccess(response) {
			this.computedImageUrl = response.data;
		},
		beforeAvatarUpload(rawFile) {
			if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
				this.$message.error('头像图片必须是JPG或PNG格式!');
				return false;
			}
			return true;
		},
		async fetchWorkgroups() {
			this.workgroupsLoading = true;
			try {
				// 获取用户加入的工作组
				const response = await groupApi.getJoinedList();
				this.workgroups = response?.data || [];

				// 同时获取所有工作组（用于展示当前选中工作组的名称）
				await this.fetchAllWorkgroups();
			} catch (error) {
				console.error('获取工作组列表错误:', error);
				ElMessage.error('获取工作组列表失败');
			} finally {
				this.workgroupsLoading = false;
			}
		},
		async fetchAllWorkgroups() {
			this.allWorkgroupsLoading = true;
			try {
				const response = await groupApi.getAllGroupsList();
				this.allWorkgroups = response?.data || [];
			} catch (error) {
				console.error('获取所有工作组列表错误:', error);
			} finally {
				this.allWorkgroupsLoading = false;
			}
		},
		handleGroupChange(value) {
			this.$emit('update:groupId', value);
		}
	},
	mounted() {
		this.fetchWorkgroups();
		if (this.groupId) {
			this.selectedGroupId = typeof this.groupId === 'string' ? parseInt(this.groupId, 10) : this.groupId;
		}
	},
};
</script>

<style scoped>
.basic-settings {
	border-radius: 8px;
}

.group-title {
	font-size: 14px;
	color: var(--el-text-color-secondary);
	margin: 5px;
	font-weight: 500;
}

.section-header {
	display: flex;
	align-items: center;
}

.avatar-uploader .el-upload {
	/* border: 1px dashed var(--el-border-color); */
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
	/* border-color: var(--el-color-primary); */
}

.el-icon.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 100px;
	height: 100px;
	text-align: center;
}

.avatar-uploader .avatar {
	width: 100px;
	height: 100px;
	display: block;
}

.description-input {
	margin-top: 10px;
}

.owner-row {
	margin-top: 15px;
	display: flex;
	align-items: center;
}

.label {
	display: inline-block;
	font-size: 14px;
	color: var(--el-text-color-regular);
}

.image-tip {
	margin-top: 10px;
	display: inline-block;
}
</style>

<template>
	<v-md-editor
		v-model="localValue"
		:height="height"
		left-toolbar="undo redo clear | h bold italic strikethrough quote | ul ol table hr | link image code"
		@change="handleChange"
	/>
</template>

<script>
import { ref, watch } from 'vue';
import VMdEditor from '@kangc/v-md-editor';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github';
import '@kangc/v-md-editor/lib/theme/style/github.css';

// 引入需要的语言高亮支持
import hljs from 'highlight.js';
import json from 'highlight.js/lib/languages/json';
import javascript from 'highlight.js/lib/languages/javascript';
import bash from 'highlight.js/lib/languages/bash';

// 注册语言
hljs.registerLanguage('json', json);
hljs.registerLanguage('javascript', javascript);
hljs.registerLanguage('bash', bash);

VMdEditor.use(githubTheme, {
	Hljs: hljs,
});

export default {
	props: {
		modelValue: String,
		height: {
			type: String,
			default: '300px',
		},
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		const localValue = ref(props.modelValue || '');

		watch(
			() => props.modelValue,
			(newVal) => {
				localValue.value = newVal;
			}
		);

		const handleChange = (value) => {
			emit('update:modelValue', value);
		};

		return { localValue, handleChange };
	},
};
</script>

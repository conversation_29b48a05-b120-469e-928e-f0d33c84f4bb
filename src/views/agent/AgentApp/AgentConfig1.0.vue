<!-- eslint-disable -->

<template>
	<div class="agent-config">
		<el-row class="config-row" :gutter="10">
			<el-col :span="12" class="config-column" style="border-right: 0.5px solid #cccccc">
				<div class="info">
					<el-descriptions>
						<template #title>
							<div class="firstTitle">
								<el-icon><UserFilled /></el-icon>
								<span>应用设定</span>
							</div>
						</template>
						<el-descriptions-item label="基本信息" :column="24">
							<!-- eslint-disable-next-line  -->
							<basic-settings
								:baseURL="baseURL"
								:image-url="agentData.imageUrl"
								:agent-name="agentData.name"
								:description="agentData.description"
								:owner="agentData.owner"
								@update:imageUrl="updateField('imageUrl', $event)"
								@update:agentName="updateField('name', $event)"
								@update:description="updateField('description', $event)"
								@update:owner="updateField('owner', $event)"
							/>
						</el-descriptions-item>
						<el-descriptions-item label="模型选择" :column="24">
							<model-settings
								:model-options="modelOptions"
								:model-id="agentData.modelId"
								:rule-prompt="agentData.rulePrompt"
								:response-prompt="agentData.responsePrompt"
								@update:modelId="updateField('modelId', $event)"
								@update:rolePrompt="updateField('rolePrompt', $event)"
								@update:rulePrompt="updateField('rulePrompt', $event)"
								@update:responsePrompt="updateField('responsePrompt', $event)"
							/>
						</el-descriptions-item>
					</el-descriptions>
				</div>
			</el-col>
			<el-col :span="12" class="config-column">
				<div class="info">
					<el-descriptions>
						<template #title>
							<div class="firstTitle">
								<el-icon><Tools /></el-icon>
								<span>能力拓展</span>
							</div>
						</template>
						<el-descriptions-item label="知识" :column="24">
							<knowledge-settings
								:recall-count="agentData.recallCount"
								:similarity="agentData.similarity"
								:selected-knowledge-bases="agentData.selectedRows"
								@update:recallCount="updateField('recallCount', $event)"
								@update:similarity="updateField('similarity', $event)"
								@openAssociationDialog="dialogTableVisible = true"
							/>
						</el-descriptions-item>
					</el-descriptions>
				</div>
			</el-col>
		</el-row>
	</div>
</template>

<script lang="ts">
	import { ref } from 'vue';
	import BasicSettings from './BasicSettings.vue';
	import ModelSettings from './ModelSettings.vue';
	import KnowledgeSettings from './KnowledgeSettings.vue';

	export default {
		name: 'AgentConfig',
		components: { BasicSettings, ModelSettings, KnowledgeSettings },
		props: {
			agentData: {
				type: Object,
				required: true,
			},
			modelOptions: {
				type: Array,
				default: () => [],
			},
			baseURL: {
				type: String,
				default: '',
			},
		},
		emits: ['update:agentData'],
		setup(props, { emit }) {
			const dialogTableVisible = ref(false);

			const updateField = (field, value) => {
				emit('update:agentData', { ...props.agentData, [field]: value });
			};

			return {
				dialogTableVisible,
				updateField,
			};
		},
	};
</script>

<style scoped>
	.agent-config {
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	.config-row {
		display: flex;
		flex: 1;
		align-items: stretch; /* 使子项等高 */
		margin: 0 !important; /* 覆盖element-plus的默认margin */
	}

	.config-column {
		height: calc(100% - 120px);
		display: flex;
		flex-direction: column;
		border-right: 0.5px solid #cccccc;
	}

	/* 修改原有样式 */
	.info {
		flex: 1; /* 填充可用空间 */
		background-color: white;
		padding-bottom: 20px; /* 添加底部内边距防止内容贴底 */
		overflow-y: auto; /* 如果内容过多可以滚动 */
	}

	/* 移除原有边框设置 */
	.el-col:first-child {
		border-right: none;
	}

	.slider-demo-block {
		flex-direction: column; /* 让子元素垂直排列 */
		gap: 10px; /* 设置间距 */
		display: flex;
		align-items: start;
		padding: 10px;
	}
	.slider-demo-block .el-slider {
	}
	.slider-demo-block .demonstration {
		font-size: 14px;
		color: var(--el-text-color-secondary);
		line-height: 44px;
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-bottom: 0;
	}
	.slider-demo-block .demonstration + .el-slider {
		flex: 0 0 70%;
	}

	.avatar-uploader .el-upload {
		/* border: 1px dashed var(--el-border-color); */
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		transition: var(--el-transition-duration-fast);
	}

	.avatar-uploader .el-upload:hover {
		/* border-color: var(--el-color-primary); */
	}

	.el-icon.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 100px;
		height: 100px;
		text-align: center;
	}

	.avatar-uploader .avatar {
		width: 100px;
		height: 100px;
		display: block;
	}

	.firstTitle {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		height: 24px;
		padding: 16px 10px 4px;
		color: #000;
		font-size: 14px;
		font-weight: 600;
		line-height: 24px;
		gap: 8px;
	}
</style>

<!-- eslint-disable -->
<template>
	<div class="model-settings">
		<el-card style="padding: 10px">
			<div class="section-header">
				<el-button type="primary" text :icon="Edit" @click="openPromptDialog">用户指令</el-button>
			</div>

			<el-row class="model-selection">
				<el-col :span="6">
					<span class="selection-label">生成模型</span>
				</el-col>
				<el-col :span="16" :offset="2">
					<el-select v-model="selectedModel" placeholder="选择模型" size="large" class="model-selector">
						<el-option
							v-for="item in modelOptions"
							:key="item.id"
							:label="${item.platform}-${item.name}"
							:value="item.id"
						/>
					</el-select>
				</el-col>
			</el-row>
		</el-card>

		<!-- Prompt Editing Dialog -->
		<el-dialog v-model="dialogVisible" title="模型交互设置" width="70%">
			<el-row class="model-selection">
				<el-col :span="4">
					<span class="selection-label">用户角色</span>
				</el-col>
				<el-col :span="20">
					<el-input v-model="rolePrompt" />
				</el-col>
			</el-row>

			<el-row class="model-selection">
				<el-col :span="4">
					<span class="selection-label">用户指令(JSON)</span>
				</el-col>
				<el-col :span="20">
					<monaco-editor
						v-model="currentRulePromptJson"
						language="json"
						:options="editorOptions"
						@validate="onJsonValidate"
						style="height: 200px"
					/>
					<div v-if="ruleJsonError" class="error-message">{{ ruleJsonError }}</div>
				</el-col>
			</el-row>

			<el-row class="model-selection">
				<el-col :span="4">
					<span class="selection-label">应用回复(JSON)</span>
				</el-col>
				<el-col :span="20">
					<monaco-editor
						v-model="currentResponsePromptJson"
						language="json"
						:options="editorOptions"
						@validate="onJsonValidate"
						style="height: 200px"
					/>
					<div v-if="responseJsonError" class="error-message">{{ responseJsonError }}</div>
				</el-col>
			</el-row>

			<template #footer>
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="savePrompts" :disabled="hasJsonErrors">保存</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script>
import { ref, watch, computed, defineAsyncComponent } from 'vue';
import { Edit } from '@element-plus/icons-vue';

export default {
	name: 'ModelSettings',
	components: {
		MonacoEditor: defineAsyncComponent({
			loader: () => import('monaco-editor-vue3').then((m) => m.default),
			loadingComponent: {
				template: '<div>编辑器加载中...</div>',
			},
			errorComponent: {
				template: '<div>编辑器加载失败</div>',
			},
			delay: 200,
			timeout: 3000,
		}),
	},
	props: {
		modelOptions: Array,
		modelId: String,
		rolePrompt: String,
		rulePrompt: String,
		responsePrompt: String,
	},
	emits: ['update:modelId', 'update:rolePrompt', 'update:rulePrompt', 'update:responsePrompt'],
	setup(props, { emit }) {
		const selectedModel = ref(props.modelId || '');
		const dialogVisible = ref(false);
		const rolePrompt = ref(props.rolePrompt || '');
		const currentRulePrompt = ref(props.rulePrompt || '');
		const currentResponsePrompt = ref(props.responsePrompt || '');
		const currentRulePromptJson = ref('');
		const currentResponsePromptJson = ref('');
		const ruleJsonError = ref('');
		const responseJsonError = ref('');

		const editorOptions = ref({
			automaticLayout: true,
			minimap: { enabled: false },
			scrollBeyondLastLine: false,
			fontSize: 14,
			lineNumbers: 'off',
			folding: false,
			renderLineHighlight: 'none',
		});

		const hasJsonErrors = computed(() => ruleJsonError.value || responseJsonError.value);

		// Watchers
		watch(
			() => props.modelOptions,
			(newOptions) => {
				if (newOptions.length > 0) {
					const found = newOptions.find((opt) => opt.id === props.modelId);
					selectedModel.value = found ? found.id : newOptions[0].id;
					if (!found) {
						emit('update:modelId', selectedModel.value);
					}
				}
			},
			{ immediate: true }
		);

		watch(
			() => props.modelId,
			(val) => (selectedModel.value = val)
		);
		watch(selectedModel, (val) => emit('update:modelId', val));

		const openPromptDialog = () => {
			currentRulePrompt.value = props.rulePrompt;
			currentResponsePrompt.value = props.responsePrompt;

			try {
				currentRulePromptJson.value = JSON.stringify(JSON.parse(currentRulePrompt.value), null, 2);
			} catch {
				currentRulePromptJson.value = '{\n  "instruction": "",\n  "parameters": {}\n}';
			}

			try {
				currentResponsePromptJson.value = JSON.stringify(JSON.parse(currentResponsePrompt.value), null, 2);
			} catch {
				currentResponsePromptJson.value = '{\n  "response": "",\n  "format": {}\n}';
			}

			dialogVisible.value = true;
		};

		const onJsonValidate = (markers) => {
			ruleJsonError.value = '';
			responseJsonError.value = '';
			for (const marker of markers) {
				const { owner, message } = marker;
				if (owner.includes('rule')) {
					ruleJsonError.value = message;
				}
				if (owner.includes('response')) {
					responseJsonError.value = message;
				}
			}
		};

		const savePrompts = () => {
			try {
				currentRulePrompt.value = JSON.stringify(JSON.parse(currentRulePromptJson.value));
				currentResponsePrompt.value = JSON.stringify(JSON.parse(currentResponsePromptJson.value));
			} catch (e) {
				console.error('JSON parsing error:', e);
				return;
			}
			emit('update:rolePrompt', rolePrompt.value);
			emit('update:rulePrompt', currentRulePrompt.value);
			emit('update:responsePrompt', currentResponsePrompt.value);
			dialogVisible.value = false;
		};

		return {
			Edit,
			selectedModel,
			dialogVisible,
			// eslint-disable-next-line
			rolePrompt,
			currentRulePromptJson,
			currentResponsePromptJson,
			ruleJsonError,
			responseJsonError,
			editorOptions,
			openPromptDialog,
			savePrompts,
			onJsonValidate,
			hasJsonErrors,
		};
	},
};
</script>

<style scoped>
.model-settings {
	border-radius: 8px;
}

.section-header {
	display: flex;
	align-items: center;
	padding: 10px 0;
}

.model-selection {
	margin-top: 10px;
	display: flex;
	align-items: center;
}

.selection-label {
	font-size: 14px;
	color: var(--el-text-color-secondary);
}

.model-selector {
	width: 100%;
}

.error-message {
	color: #f56c6c;
	font-size: 12px;
	margin-top: 5px;
}
</style>

<!-- eslint-disable -->
<template>
	<el-row class="upper-row">
		<el-col :span="24">
			<el-row class="header-content">
				<el-col :span="3" class="header-title">
					<span>我的Agent应用</span>
				</el-col>
				<el-col :span="17"></el-col>
				<el-col :span="3" class="header-actions">
					<el-button v-if="isLoadingFaBu" type="primary"
						class="publish-btn"
						@click="saveAgent"
					>
						发布
					</el-button>
					<el-button v-if="isLoadingUpdate" type="primary"
						class="publish-btn"
						@click="updateAgent"
					>
						更新
					</el-button>
				</el-col>
				<el-col :span="1"></el-col>
			</el-row>
		</el-col>
	</el-row>
</template>

<script>
export default {
	props: {
		isLoadingFaBu: Boolean,
		isLoadingUpdate: <PERSON>olean,
	},
	methods: {
		saveAgent() {
			this.$emit('save');
		},
		updateAgent() {
			this.$emit('update');
		},
	},
};
</script>

<style scoped>
.upper-row {
	/* height: 10%; */
	margin-bottom: 10px;
}

.header-content {
	background-color: white;
	margin-top: 5px;
}

.header-title {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 10px;
	font-size: 16px;
}

.header-actions {
	display: flex;
	justify-content: flex-end;
	padding: 10px;
}

.publish-btn {
	width: 200px;
	height: 35px;
	padding: 5px;
}
</style>

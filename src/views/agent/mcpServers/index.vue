<!-- eslint-disable -->
<template>
	<div class="app-workspace">
		<!-- 标签页 -->
		<el-tabs v-model="tabActive" class="server-tabs" @tab-click="handleTabClick">
			<el-tab-pane label="我的" name="mine" :lazy="true" />
			<el-tab-pane label="全部" name="all" :lazy="true" />
		</el-tabs>
		<!-- 查询组件 -->
		<McpServerQueryForm :query="queryParams" :tab-active="tabActive" @search="handleSearch" @create="handleCreate" />

		<!-- 表格组件 -->
		<McpServerTable
			:table-data="paging.items"
			:loading="loading"
			:paging="paging"
			@edit="handleEdit"
			@delete="handleDelete"
			@update:page="handlePageChange"
			@update:size="handleSizeChange"
		/>

		<!-- 新建/编辑对话框 -->
		<McpServerForm
			v-model="dialogVisible"
			:title="dialogTitle"
			:server="currentServer"
			@save="handleSave"
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import McpServerQueryForm from '@/views/agent/mcpServers/components/McpServerQueryForm.vue';
import McpServerTable from '@/views/agent/mcpServers/components/McpServerTable.vue';
import McpServerForm from '@/views/agent/mcpServers/components/McpServerForm.vue';
import mcpServersApi from '@/api/McpServersApi';

const loading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const tabActive = ref('mine');

const queryParams = reactive({
	name: '',
	groupId: '',
	page: '1',
	size: '10',
	type: 1, // 默认查询个人
});

const paging = reactive({
	page: 1,
	size: 10,
	total: 0,
	items: [],
});

const currentServer = ref({
	id: '',
	name: '',
	type: '',
	command: '',
	url: '',
	env: '',
	queryPath: '',
	executePath: '',
	groupId: '',
	description: '',
});

onMounted(() => {
	getServers();
});

// 处理标签页切换
const handleTabClick = (tab: { props: { name: string } }) => {
	queryParams.type = tab.props.name === 'all' ? 0 : 1;
	queryParams.page = '1';
	getServers();
};

const handlePageChange = (page) => {
	queryParams.page = page.toString();
	getServers();
};

const handleSizeChange = (size) => {
	queryParams.size = size.toString();
	queryParams.page = '1';
	getServers();
};

const getServers = async (query = queryParams) => {
	loading.value = true;
	try {
		const resp = await mcpServersApi.getServers(query);
		if (resp?.code === 200) {
			Object.assign(paging, resp.data);
		}
	} catch (error) {
		console.error('Failed to fetch servers:', error);
	} finally {
		loading.value = false;
	}
};

const handleSearch = () => {
	queryParams.page = '1';
	getServers();
};

const handleCreate = () => {
	dialogTitle.value = '新建服务器';
	currentServer.value = {
		id: '',
		name: '',
		type: '',
		command: '',
		url: '',
		env: '',
		queryPath: '',
		executePath: '',
		groupId: '',
		description: '',
	};
	dialogVisible.value = true;
};

const handleEdit = (server) => {
	dialogTitle.value = '编辑服务器';
	currentServer.value = { ...server };
	dialogVisible.value = true;
};

const handleSave = async (server) => {
	try {
		const apiMethod = server.id ? mcpServersApi.edit : mcpServersApi.create;
		const resp = await apiMethod(server);
		if (resp?.code === 200) {
			getServers();
			dialogVisible.value = false;
			ElMessage.success(resp?.message || '操作成功');
		}
	} catch (error) {
		console.error('Failed to save server:', error);
		ElMessage.error('操作失败，请检查数据');
	}
};

const handleDelete = async (server) => {
	try {
		const resp = await mcpServersApi.remove({
			id: server.id,
			groupId: server.groupId,
		});
		if (resp?.code === 200) {
			getServers();
			ElMessage.success('删除成功');
		}
	} catch (error) {
		console.error('Failed to delete server:', error);
		ElMessage.error('删除失败');
	}
};

</script>

<style scoped>
.app-workspace {
	padding: 20px;
}

.server-tabs {
	margin-bottom: 20px;
}

.server-tabs > .el-tabs__content {
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}
</style>
<!-- eslint-disable -->
<template>
	<div class="input-container">
		<el-row justify="center">
			<el-col :span="22" style="border-radius: 12px">
				<el-card :body-style="{ padding: '0px' }" class="fixed-input">
					<div
						class="grid-content bg-purple-dark"
						style="margin: 10px; display: flex; justify-content: flex-end; align-items: center"
					>
						<el-input
							v-model="inputPrompt"
							placeholder="输入你的问题..."
							style="height: 40px; border: none !important; flex: 1"
							@keyup.enter="onSubmit"
							:disabled="isLoading"
						></el-input>
						<el-button
							link
							@click="onSubmit"
							style="width: 40px"
							:loading="isLoading"
							:disabled="!inputPrompt?.trim() || isLoading"
						>
							<img
								src="@/assets/play.png"
								height="25px"
								width="25px"
								v-if="!isLoading"
							/>
						</el-button>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script lang="ts">
	import { defineComponent, ref, watch } from 'vue';
	import { ElMessage } from 'element-plus';

	export default defineComponent({
		name: 'AgentInput',
		props: {
			isLoading: Boolean,
			agentId: {
				type: [Number, String],
				default: 0,
			},
			modelValue: String, // 用于v-model绑定
		},
		emits: ['submit', 'update:modelValue'],
		setup(props, { emit }) {
			const inputPrompt = ref(props.modelValue || '');

			const onSubmit = () => {
				console.log('onSubmit', inputPrompt.value);
				if (inputPrompt.value?.trim()) {
					emit('submit', {
						prompt: inputPrompt.value,
						agentId: props.agentId || 0,
					});
				} else {
					ElMessage.error('请输入问题');
				}
			};

			// 监听modelValue变化
			watch(
				() => props.modelValue,
				(newVal) => {
					inputPrompt.value = newVal || '';
				},
			);

			// 监听inputPrompt变化
			watch(inputPrompt, (newVal) => {
				emit('update:modelValue', newVal);
			});

			return {
				inputPrompt,
				onSubmit,
			};
		},
	});
</script>

<style scoped>
	.input-container {
		width: 100%;
		position: absolute;
		bottom: 10px;
		left: 0;
		right: 0;
		/* padding: 0 10px 5px; */
		z-index: 100;
	}

	.fixed-input {
		width: 100%;
		/* box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); */
	}
</style>
<template>
	<div style="margin: 5px; background: #fff; padding: 10px">
		<div class="search-row">
			<el-input style="width: 250px" v-model="keyword" placeholder="请输入关键词" clearable> </el-input>
			<el-icon class="search-icon" @click="getAgentData">
				<Search />
			</el-icon>
			<el-button type="primary" class="add-btn" :onclick="handleXinJianClick"> 新建 </el-button>
		</div>

		<el-tabs v-model="tabActive" class="agent-tabs" @tab-click="getAgentData">
			<el-tab-pane label="我发布的" name="mine" :lazy="true" />
			<el-tab-pane label="全部" name="all" :lazy="true" />
		</el-tabs>

		<div class="agent-list-container">
			<agent-cards
				:key="tabActive"
				:tab-name="tabActive"
				:detail="agents"
				@update-page-size="handleUpdatePageSize"
			/>
		</div>
		
		<!-- 分页控件 -->
		<div class="pagination-container">
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:page-sizes="pageSizes"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>
	</div>
</template>
<script lang="ts">
import Cards from '@/views/agent/card/Cards.vue';
import type { queryAgentParam, queryAgentResponseData } from '@/types/AgentTypes';
import agentApi from '@/api/AgentApi';
import { Search } from '@element-plus/icons-vue';

export default {
	components: {
		AgentCards: Cards,
		Search
	},
	created() {
		// 先计算合适的页面大小，默认值将在组件加载后被覆盖
		this.getAgentData(null);
	},
	data() {
		return {
			keyword: '',
			tabActive: 'mine',
			agents: [] as queryAgentResponseData[],
			// 分页相关数据
			currentPage: 1,
			pageSize: 12, // 默认值，会被动态计算的值覆盖
			pageSizes: [12, 24, 36], // 默认值，会被动态计算的值覆盖
			total: 0,
		};
	},
	methods: {
		handleXinJianClick() {
			// Navigate to apps/update
			this.$router.push('/apps/update');
		},
		// 处理Cards组件发送的页面大小更新
		handleUpdatePageSize(size: number) {
			this.pageSize = size;
			this.pageSizes = [size, size * 2, size * 3];
			// 重新请求数据
			this.currentPage = 1;
			this.getAgentData(null);
		},
		// 新的分页查询方法
		getAgentData(tab?: { props: { name: string } } | null) {
			// 如果传入了tab参数，更新当前tabActive
			if (tab?.props?.name) {
				this.tabActive = tab.props.name;
			}

			const data = {
				type: this.tabActive === 'all' ? 0 : 1,
				page: this.currentPage,
				size: this.pageSize,
				keyword: this.keyword,
			};
			agentApi.getAgentPaging(data).then((res: any) => {
				if (res.code === 200) {
					this.agents = [];
					// 服务端返回的数据格式为 {total: number, items: []}
					this.total = res.data.total || 0;
					const items = res.data.items || [];
					items.forEach((agent: queryAgentResponseData) => {
						this.agents.push(agent);
					});
				}
			});
		},
		// 处理页码变化
		handleCurrentChange(val: number) {
			this.currentPage = val;
			this.getAgentData(null);
		},
		// 处理每页显示数量变化
		handleSizeChange(val: number) {
			this.pageSize = val;
			this.currentPage = 1; // 重置为第一页
			this.getAgentData(null);
		},
	},
};
</script>

<style>
.search-row {
	position: absolute;
	display: flex;
	top: 75px;
	right: 20px;
	z-index: 2;
}

.search-row .search-icon {
	margin: 8px 10px 0 10px;
	color: #909399;
	cursor: pointer;
}

.agent-tabs > .el-tabs__content {
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}

/* 添加滚动容器样式 */
.agent-list-container {
	max-height: calc(100vh - 200px);
	overflow-y: auto;
	padding: 0 5px;
	margin-right: -5px; /* 减小右侧边距 */
	scrollbar-width: thin; /* Firefox */
	scrollbar-color: #909399 #f4f4f5; /* Firefox */
}

/* 自定义滚动条样式 - Webkit浏览器 */
.agent-list-container::-webkit-scrollbar {
	width: 6px;
}

.agent-list-container::-webkit-scrollbar-track {
	background: #f4f4f5;
	border-radius: 3px;
}

.agent-list-container::-webkit-scrollbar-thumb {
	background-color: #909399;
	border-radius: 3px;
}

.agent-list-container::-webkit-scrollbar-thumb:hover {
	background-color: #606266;
}

.agent-card {
	margin-bottom: 10px;
}

.agent-card .agent-img {
	width: 100px;
}

.agent-card .el-card__body {
	padding: 5px;
}

.agent-card .agent-info .agent-title {
	font-weight: bold;
}

.agent-card .agent-info .agent-opt {
	/* border-top: 1px solid #e5e7e9; */
	text-align: left;
}

.agent-card .agent-info .el-descriptions__body .el-descriptions__cell {
	padding-bottom: 0;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}
</style>

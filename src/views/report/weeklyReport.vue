<template>
  <div class="weekly-report-container">


    <!-- 数据概览 -->
    <div class="summary-section" v-loading="loading">
      <div class="summary-header">
        <div class="summary-title">
          <h2>数据概览</h2>
          <div class="update-time">
            <!-- <span class="time-indicator"></span> -->
            数据统计截止到：{{ getCurrentDate() }}
          </div>
        </div>
        <div class="refresh-action">
          <el-button type="default" @click="copyTableAsMarkdown" size="default">
            <el-icon>
              <CopyDocument />
            </el-icon>
            复制表格
          </el-button>
          <el-button type="primary" :loading="loading" @click="refreshData(true)" size="default">
            <el-icon>
              <Refresh />
            </el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
      <div class="summary-cards">
        <div class="summary-card">
          <div class="card-header">
            <span class="card-title">用户总数</span>
          </div>
          <div class="summary-number user-color">{{ formatNumber(summaryData.userCount) }}</div>
        </div>
        <div class="summary-card">
          <div class="card-header">
            <span class="card-title">知识库总数</span>
          </div>
          <div class="summary-number kb-color">{{ formatNumber(summaryData.kbCount) }}</div>
        </div>
        <div class="summary-card">
          <div class="card-header">
            <span class="card-title">智能体总数</span>
          </div>
          <div class="summary-number agent-color">{{ formatNumber(summaryData.agentCount) }}</div>
        </div>
        <div class="summary-card">
          <div class="card-header">
            <span class="card-title">文档总数</span>
          </div>
          <div class="summary-number doc-color">{{ formatNumber(summaryData.docCount) }}</div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-container">
        <el-table :data="dataCards" style="width: 100%" class="report-table">
          <el-table-column prop="title" label="指标项" width="200" align="left">
            <template #default="scope">
              <span class="metric-title">{{ scope.row.title }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="data.preQ" label="上个Q基线" align="center">
            <template #default="scope">
              <span class="metric-number">{{ formatNumber(scope.row.data.preQ) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="data.lastWeek" label="QTD上周" align="center">
            <template #default="scope">
              <span class="metric-number">{{ formatNumber(scope.row.data.lastWeek) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="data.curWeek" label="QTD本周" align="center">
            <template #default="scope">
              <span class="metric-number">
                {{ formatNumber(scope.row.data.curWeek) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Refresh, Folder, Document, Avatar, Lightning, Upload, Download, CopyDocument } from '@element-plus/icons-vue'
import weeklyReportApi, { type WeeklyReportData } from '../../api/WeeklyReportApi'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)

// 概览数据
const summaryData = ref({
  userCount: 0,
  kbCount: 0,
  agentCount: 0,
  docCount: 0
})

// 格式化日期为本地时间字符串
const formatLocalDateTime = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 计算当前季度信息
const getCurrentQuarterInfo = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() // 0-11

  // 计算季度
  const quarter = Math.floor(month / 3) + 1

  // 计算季度第一天
  const quarterStartMonth = (quarter - 1) * 3
  const firstDayOfQuarter = new Date(year, quarterStartMonth, 1, 0, 0, 0)

  return {
    quarter,
    year,
    firstDayOfQuarter: formatLocalDateTime(firstDayOfQuarter),
    currentDate: formatLocalDateTime(now)
  }
}

const quarterInfo = getCurrentQuarterInfo()
console.log('季度信息:', quarterInfo)
const currentQuarter = ref(`${quarterInfo.year}年Q${quarterInfo.quarter}`)
const quarterDateRange = ref(`${quarterInfo.firstDayOfQuarter.split(' ')[0]} 至 ${quarterInfo.currentDate.split(' ')[0]}`)

// 数据卡片配置
const dataCards = ref([
  {
    title: '用户数',
    icon: Avatar,
    color: '#1890ff',
    data: { preQ: 0, curWeek: 0, lastWeek: 0 }
  },
  {
    title: '知识库数',
    icon: Folder,
    color: '#52c41a',
    data: { preQ: 0, curWeek: 0, lastWeek: 0 }
  },
  {
    title: '文档数',
    icon: Document,
    color: '#722ed1',
    data: { preQ: 0, curWeek: 0, lastWeek: 0 }
  },
  {
    title: '智能体数',
    icon: Avatar,
    color: '#fa8c16',
    data: { preQ: 0, curWeek: 0, lastWeek: 0 }
  },
  {
    title: '任务数',
    icon: Lightning,
    color: '#eb2f96',
    data: { preQ: 0, curWeek: 0, lastWeek: 0 }
  },
  {
    title: '输入Token数',
    icon: Upload,
    color: '#13c2c2',
    data: { preQ: 0, curWeek: 0, lastWeek: 0 }
  },
  {
    title: '输出Token数',
    icon: Download,
    color: '#f5222d',
    data: { preQ: 0, curWeek: 0, lastWeek: 0 }
  }
])

// 格式化数字显示
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 获取当前日期
const getCurrentDate = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}年${month}月${day}日`
}

// API调用函数
const fetchWeeklyReportData = async (): Promise<WeeklyReportData> => {
  const quarterInfo = getCurrentQuarterInfo()

  try {
    const response = await weeklyReportApi.getWeeklyReportData({
      firstDayOfQuarter: quarterInfo.firstDayOfQuarter,
      currentDate: quarterInfo.currentDate
    })

    return response.data
  } catch (error) {
    console.error('API调用失败:', error)
    throw error
  }
}

// 更新数据卡片
const updateDataCards = (apiData: WeeklyReportData) => {
  const cardKeys = ['userNum', 'kbNum', 'docNum', 'agentNum', 'taskNum', 'inputTokenNum', 'outputTokenNum']

  cardKeys.forEach((key, index) => {
    if (apiData[key as keyof WeeklyReportData] && dataCards.value[index]) {
      const cardData = apiData[key as keyof WeeklyReportData]

      dataCards.value[index].data = {
        preQ: cardData[`${key}PreQ` as keyof typeof cardData] as number,
        curWeek: cardData[`${key}CurWeek` as keyof typeof cardData] as number,
        lastWeek: cardData[`${key}LastWeek` as keyof typeof cardData] as number
      }
    }
  })

  // 更新概览数据（使用总数）
  summaryData.value = {
    userCount: apiData.userNum?.userNumTotal || 0,
    kbCount: apiData.kbNum?.kbNumTotal || 0,
    agentCount: apiData.agentNum?.agentNumTotal || 0,
    docCount: apiData.docNum?.docNumTotal || 0
  }
}

// 复制表格为Markdown格式
const copyTableAsMarkdown = async () => {
  try {
    // 构建Markdown表格
    let markdown = '| 指标项 | 上个季度 | QTD上周 | QTD本周 |\n'
    markdown += '|-------|---------|---------|----------|\n'

    dataCards.value.forEach(card => {
      const title = card.title
      const preQ = formatNumber(card.data.preQ)
      const lastWeek = formatNumber(card.data.lastWeek)
      const curWeek = formatNumber(card.data.curWeek)

      markdown += `| ${title} | ${preQ} | ${lastWeek} | ${curWeek} |\n`
    })

    // 添加数据统计时间
    // markdown += `\n*数据统计截止到：${getCurrentDate()}*`

    // 复制到剪贴板
    await navigator.clipboard.writeText(markdown)
    ElMessage.success('表格已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请稍后重试')
  }
}

// 刷新数据
const refreshData = async (showSuccessMessage = false) => {
  loading.value = true
  try {
    const apiData = await fetchWeeklyReportData()
    updateDataCards(apiData)
    if (showSuccessMessage) {
      ElMessage.success('数据刷新成功')
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('数据刷新失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 自动加载数据，不显示成功提示
  refreshData(false)
})
</script>

<style scoped>
.weekly-report-container {
  padding: 10px;
  background: #f8fafc;
  height: calc(100vh - 80px);
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.weekly-report-container::-webkit-scrollbar {
  width: 6px;
}

.weekly-report-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.weekly-report-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.weekly-report-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.summary-section {
  margin-bottom: 10px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 16px;
  /* border-bottom: 1px solid #f1f5f9; */
}

.summary-title h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.update-time {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 13px;
}

.time-indicator {
  width: 8px;
  height: 8px;
  background: #6b7280;
  border-radius: 50%;
}

.refresh-action {
  flex-shrink: 0;
  display: flex;
}

.summary-cards {
  display: flex;
  gap: 24px;
  justify-content: space-around;
}

.summary-card {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px 24px;
  text-align: center;
  min-width: 140px;
  border: 1px solid #e2e8f0;
  flex: 1;
  transition: all 0.2s ease;
}

.summary-card:hover {
  background: #f1f5f9;
  border-color: #d1d5db;
}

.card-header {
  margin-bottom: 12px;
}

.card-title {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
}

.summary-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.summary-number.user-color {
  color: #3b82f6;
}

.summary-number.kb-color {
  color: #10b981;
}

.summary-number.agent-color {
  color: #8b5cf6;
}

.summary-number.doc-color {
  color: #f59e0b;
}

/* .table-section {
  margin-bottom: 40px;
} */

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.report-table {
  border-radius: 12px;
}

.report-table :deep(.el-table__header-wrapper) {
  background: #f8fafc;
}

.report-table :deep(.el-table__header th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  font-size: 13px;
  border-bottom: 2px solid #e2e8f0;
  padding: 14px 12px;
}

.report-table :deep(.el-table__body tr) {
  transition: background-color 0.2s ease;
}

.report-table :deep(.el-table__body tr:hover) {
  background-color: #f8fafc;
}

.report-table :deep(.el-table__body td) {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f5f9;
}

.metric-title {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.metric-number {
  color: #374151;
  font-size: 15px;
  font-weight: 600;
}

.metric-number.highlight {
  font-size: 16px;
  font-weight: 700;
}

.summary-section {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.summary-item {
  text-align: center;
  padding: 16px;
}

.summary-number {
  font-size: 48px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 8px;
}

.summary-label {
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .weekly-report-container {
    padding: 16px;
    height: calc(100vh - 100px);
  }

  /* 移动端滚动条隐藏 */
  .weekly-report-container::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  .header-section {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
    padding: 24px;
  }

  .title-area h1 {
    font-size: 28px;
  }

  .action-area {
    justify-content: flex-start;
  }

  .summary-section {
    padding: 20px;
  }

  .summary-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .refresh-action {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .refresh-action .el-button {
    width: 100%;
  }

  .summary-title h2 {
    font-size: 18px;
  }

  .update-time {
    font-size: 12px;
  }

  .summary-cards {
    flex-direction: column;
    gap: 16px;
  }

  .summary-card {
    padding: 16px 20px;
    min-width: auto;
  }

  .card-title {
    font-size: 12px;
  }

  .summary-number {
    font-size: 20px;
  }

  .table-container {
    border-radius: 8px;
  }

  .report-table :deep(.el-table__header th) {
    padding: 10px 6px;
    font-size: 11px;
  }

  .report-table :deep(.el-table__body td) {
    padding: 12px 6px;
  }

  .metric-title {
    font-size: 12px;
  }

  .metric-number {
    font-size: 13px;
  }

  .metric-number.highlight {
    font-size: 14px;
  }
}
</style>

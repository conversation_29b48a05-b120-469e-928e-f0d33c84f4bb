<template>
	<el-dialog
		:model-value="visible"
		:title="`申请加入 ${groupName}`"
		width="500px"
		@update:visible="handleUpdateVisible"
	>
		<el-form :model="formData" label-width="100px">
			<el-form-item label="申请角色" prop="role" required>
				<el-select v-model="formData.role" placeholder="请选择角色">
					<el-option label="成员" :value="0" />
					<el-option label="管理员" :value="1" />
				</el-select>
			</el-form-item>

			<el-form-item label="审批人" prop="auditor" required>
				<el-select v-model="formData.auditor" placeholder="请选择审批人" filterable>
					<el-option v-for="admin in admins" :key="admin" :label="admin" :value="admin" />
				</el-select>
			</el-form-item>

			<el-form-item label="申请理由" prop="reason">
				<el-input v-model="formData.reason" type="textarea" :rows="3" placeholder="请输入申请理由" />
			</el-form-item>
		</el-form>

		<template #footer>
			<el-button @click="handleCancel">取消</el-button>
			<el-button type="primary" @click="handleSubmit" :loading="loading">提交申请</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import GroupApi from '@/api/GroupApi';

const props = defineProps({
	visible: Boolean,
	groupId: Number,
	groupName: String,
	admins: Array as () => string[],
});

const emit = defineEmits(['update:visible', 'submit-success']);

const formData = ref({
	workGroupId: props.groupId,
	role: 0, // 默认普通用户
	auditor: '',
	reason: '',
});

const loading = ref(false);

// 提交表单
const handleSubmit = async () => {
	try {
		loading.value = true;
		await GroupApi.submitApplication({ workGroupId: props.groupId, ...formData.value });
		ElMessage.success('申请提交成功');
		emit('update:visible', false); // 提交成功后关闭弹窗
		emit('submit-success');
	} catch (error) {
		ElMessage.error('提交申请失败');
	} finally {
		loading.value = false;
	}
};

// 更新弹窗的显示状态
const handleUpdateVisible = (val: boolean) => {
	console.log('子组件接收到弹窗状态更新:', val); // 调试信息
	emit('update:visible', val);
};

// 取消按钮的处理
const handleCancel = () => {
	emit('update:visible', false); // 点击取消关闭弹窗
};

// 监听 visible 的变化，重置表单数据
watch(
	() => props.visible,
	(val) => {
		console.log('子组件 visible 状态:', val); // 调试信息
		if (val) {
			formData.value = {
				workGroupId: props.groupId,
				role: 1,
				auditor: props.admins[0] || '',
				reason: '申请加入',
			};
		}
	}
);
</script>

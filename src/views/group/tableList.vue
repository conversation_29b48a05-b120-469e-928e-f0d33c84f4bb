<!-- eslint-disable -->
<template>
	<el-table :data="tableData" v-loading="loading" style="width: 100%">
		<el-table-column property="workGroupId" label="组ID" show-overflow-tooltip />
		<el-table-column property="workGroupName" label="组名称" show-overflow-tooltip />
		<el-table-column property="role" label="申请角色" show-overflow-tooltip>
			<template #default="scope">
				<el-tag size="small" :type="+scope.row.role === 1 ? 'primary' : 'success'">
					{{ +scope.row.role === 1 ? '管理员' : '成员' }}
				</el-tag>
			</template>
		</el-table-column>
		<el-table-column property="reason" label="申请原因" show-overflow-tooltip />
		<el-table-column property="userName" label="申请人" show-overflow-tooltip />
		<el-table-column label="操作" fixed="right" width="200">
			<template #default="scope">
				<el-button size="small" type="primary" @click="handleList(scope.row, 0)">同意</el-button>
				<el-button size="small" type="dangour" @click="handleList(scope.row, 1)">拒绝</el-button>
			</template>
		</el-table-column>
	</el-table>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import GroupApi from '@/api/GroupApi';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
import { useMessageStore } from '@/stores/messageStore';
const messageStore = useMessageStore();
const userStore = useUserStore();
// 响应式解构（避免丢失响应性）
const { username } = userStore;
const tableData = ref([]);
const loading = ref(false);

async function getTableData() {
	loading.value = true;
	try {
		const res = await GroupApi.checkList();
		tableData.value = res.data;
	} catch (error) {
		ElMessage.error('获取数据失败');
	} finally {
		loading.value = false;
	}
}

async function handleList(row, type) {
	// 处理同意或拒绝逻辑
	try {
		const { id, workGroupId, role, userName, auditor, reason } = row;
		const params = {
			id,
			workGroupId,
			role,
			userName,
			auditor,
			reason,
		};
		const res = type === 0 ? await GroupApi.applyYes(params) : await GroupApi.applyNo(params);

		if (res.code === 200) {
			ElMessage.success('提交成功');
			// 刷新未读消息数
			await messageStore.fetchUnreadCount();
		}

		getTableData();
	} catch (error) {
		ElMessage.error('提交失败');
	}
}
getTableData();
</script>

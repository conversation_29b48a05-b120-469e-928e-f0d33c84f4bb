<!-- eslint-disable -->
<template>
	<div class="avatar-container">
		<el-upload
			class="avatar-uploader"
			:action="baseURL"
			:show-file-list="false"
			:on-success="handleAvatarSuccess"
			:before-upload="beforeAvatarUpload"
		>
			<img v-if="imageUrl" :src="imageUrl" class="avatar" />
			<el-icon v-else class="avatar-uploader-icon">
				<Plus />
			</el-icon>
		</el-upload>
	</div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watchEffect } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

const baseURL = `${window.location.origin}/rag/api/user/image/upload`;
const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	size: {
		type: Number,
		default: 40,
	},
});

const emit = defineEmits(['update:modelValue']);
const imageUrl = ref(props.modelValue);

// 同步 props.modelValue 到内部 imageUrl
watchEffect(() => {
	imageUrl.value = props.modelValue;
});

// 上传成功回调
const handleAvatarSuccess = (response: any) => {
	const url = response.data || response.data?.url;
	console.log('上传成功:', url, response);
	if (url) {
		imageUrl.value = url;
		ElMessage.success('头像上传成功');
		emit('update:modelValue', url); // 确保触发update事件
	} else {
		ElMessage.error('上传成功但未返回图片链接');
	}
};

// 上传前校验
const beforeAvatarUpload = (file: File): boolean => {
	const isImage = file.type.startsWith('image/');
	const isLt2M = file.size / 1024 / 1024 < 2;

	if (!isImage) ElMessage.error('只能上传图片格式的头像');
	if (!isLt2M) ElMessage.error('图片大小不能超过 2MB');

	return isImage && isLt2M;
};
</script>

<style scoped>
.avatar-container {
	cursor: pointer;
	text-align: center;
}

.avatar-uploader {
	display: inline-block;
}

.avatar {
	border: 1px solid #dcdfe6;
	width: 100%;
	height: auto;
	max-height: 200px;
	object-fit: cover;
	border-radius: 6px;
}

.avatar-uploader-icon {
	font-size: 100px;
	color: #8c939d;
}

/* 拖拽区域样式 */
.drag-area {
	border: 2px dashed #ccc;
	padding: 20px;
	text-align: center;
	font-size: 16px;
	color: #8c939d;
	margin-top: 10px;
	cursor: pointer;
	border-radius: 6px;
	transition: border-color 0.3s;
}

.drag-area:hover {
	border-color: #409eff;
}

.drag-text {
	font-weight: bold;
	color: #333;
}
</style>

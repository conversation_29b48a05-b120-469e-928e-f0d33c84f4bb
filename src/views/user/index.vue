<!-- eslint-disable -->
<template>
	<div class="user-info">
		<div class="left-content">
			<!-- 悬浮提示 + 点击打开上传弹窗 -->
			<el-tooltip content="点击修改头像" placement="top">
				<el-avatar class="user-icon" :src="imageUrl" :size="100" @click="dialogVisible = true" />
			</el-tooltip>
		</div>

		<div class="right-conent">
			<h2>{{ username }}</h2>
			{{ departmentName }}
		</div>

		<!-- 上传头像弹窗 -->
		<el-dialog v-model="dialogVisible" title="上传头像(点击后上传)" width="400px" center>
			<UploadImage :modelValue="imageUrl" @update:modelValue="handleUploadSuccess" />
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="dialogVisible = false">取消</el-button>
					<el-button type="primary" :loading="loading" @click="uploadUserInfo">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/user';
import UploadImage from '@/views/user/UploadImage.vue';
import uploadImageApi from '@/api/UploadIMageApi';

const userStore = useUserStore();
const { imageUrl, username, departmentName } = storeToRefs(userStore);
const dialogVisible = ref(false);
const tempImageUrl = ref(''); // 新增：临时存储新头像
const loading = ref(false);

const handleUploadSuccess = (newUrl: string) => {
	tempImageUrl.value = newUrl; // 只暂存不提交
};

const uploadUserInfo = () => {
	if (!tempImageUrl.value) {
		return ElMessage.warning('请先上传图片');
	}
	loading.value = true;
	uploadImageApi
		.uploadImage({
			name: username.value,
			hiImageUrl: tempImageUrl.value,
		})
		.then((resp) => {
			if (resp?.code === 200) {
				userStore.updateImageUrl(tempImageUrl.value);
				dialogVisible.value = false;
				tempImageUrl.value = '';
				ElMessage.success(resp?.message || '头像更新成功');
			}
		})
		.finally(() => {
			loading.value = false;
		});
};
</script>

<style lang="less" scoped>
@import '@/assets/global.css';

.user-info {
	margin: 5px;
	border-radius: 6px;
	background-color: #f3f6f9;
	background-image: url(https://agi-dev-platform-web.cdn.bcebos.com/ai_apaas/dist/img/bg_3363fe4c.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	background-position: center;
	height: 400px;
	display: flex;

	.left-content {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;

		.user-icon {
			cursor: pointer;
			border: 2px solid #ccc;
			transition: transform 0.2s;
		}

		.user-icon:hover {
			transform: scale(1.05);
		}
	}

	.right-conent {
		flex: 3;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		padding-left: 20px;
	}
}
</style>

<!-- eslint-disable -->
<template>
	<el-row class="book">
		<el-col :span="2">
			<div class="avatar">
				<el-image :src="bookImagePath" style="width: 90px; height: 90px" />
			</div>
		</el-col>
		<el-col :span="22">
			<el-descriptions :title="book.name" :column="1" size="small">
				<el-descriptions-item label="描述:">{{ book.description }}</el-descriptions-item>
			</el-descriptions>
			<el-descriptions :column="1" size="small">
				<el-descriptions-item label="关联的工作组 ID:">{{ book.groupId }}</el-descriptions-item>
			</el-descriptions>
		</el-col>
	</el-row>
</template>

<script>
	export default {
		props: {
			book: Object,
			bookImagePath: String,
		},
	};
</script>

<style scoped>
	.avatar {
		text-align: center;
	}

	.book {
		background-color: #ffffff;
		font-size: 15px;
		padding-top: 10px;
	}
</style>

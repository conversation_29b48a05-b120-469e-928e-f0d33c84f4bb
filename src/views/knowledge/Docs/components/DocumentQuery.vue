<!-- eslint-disable -->
<template>
	<el-card class="search-card" shadow="hover">
		<el-row class="query-container">
			<el-col :span="24">
				<el-form class="query-form" inline>
					<el-form-item label="文档ID:">
						<el-input
							type="text"
							v-model="query.id"
							placeholder="支持根据文档ID查询"
							clearable
							style="width: 180px"
						/>
					</el-form-item>
					<el-form-item label="文档名:">
						<el-input
							type="text"
							v-model="query.title"
							placeholder="支持根据文档名称模糊查询"
							clearable
							style="width: 220px"
						/>
					</el-form-item>
					<el-form-item label="创建人:">
						<el-input
							type="text"
							v-model="query.owner"
							placeholder="支持根据创建人模糊查询"
							clearable
							style="width: 180px"
						/>
					</el-form-item>
					<el-form-item label="状态:">
						<el-select v-model="query.status" placeholder="请选择状态" style="width: 120px" clearable>
							<el-option v-for="item in statuses" :key="item.key" :label="item.value" :value="item.key" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="$emit('search')">查询</el-button>
					</el-form-item>
					<el-form-item style="float: right">
						<el-button type="primary" @click="$emit('openDrawer')">新建</el-button>
					</el-form-item>
				</el-form>
			</el-col>
		</el-row>
	</el-card>
</template>

<script>
	export default {
		props: {
			query: Object,
			statuses: Array,
		},
	};
</script>

<style scoped>
	.search-card {
		margin-bottom: 10px;
		border-radius: 4px;
	}

	.query-form .el-form-item {
		margin-right: 20px;
		margin-bottom: 0;
	}

	/* 可选：添加卡片悬停效果 */
	.search-card:hover {
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}
</style>

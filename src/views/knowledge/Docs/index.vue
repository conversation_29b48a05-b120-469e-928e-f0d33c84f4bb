<template>
	<div>
		<!-- 使用React版本的DocumentTable -->
		<!-- 只有在数据初始化完成后才渲染React组件 -->
		<react-document-table
			v-if="dataInitialized"
			:book="book"
			:statuses="statuses"
			@routeChange="handleRouteChange"
		/>

		<!-- 数据加载中的提示 -->
		<div v-else style="text-align: center; padding: 50px;">
			<div style="font-size: 16px; color: #666;">加载中...</div>
		</div>

		<document-drawer
			:show="config.drawer.show"
			@update:show="(val) => (config.drawer.show = val)"
			:title="config.drawer.title"
			:document="document"
			:files="files"
			:models="models"
			:tab-active="tabActive"
			@uploadDocuments="uploadDocuments"
			@beforeUpload="beforeUpload"
			@beforeRemove="beforeRemove"
			@editOrCreate="editOrCreate"
			@update:tab-active="handleTabChange"
		/>
		<slices-drawer
			:show="config.slices.show"
			@update:show="(val) => (config.slices.show = val)"
			:document-slices="documentSlices"
		/>
	</div>
</template>

<script>
	import DocumentDrawer from './components/DocumentDrawer.vue';
	import SlicesDrawer from './components/SlicesDrawer.vue';
	import ReactDocumentTable from '@/components/ReactDocumentTable.vue';
	import bookApi from '@/api/BookApi';
	import documentApi from '@/api/DocumentApi';
	import userApi from '@/api/UserApi';
	import modelApi from '@/api/ModelApi';
	import groupApi from '@/api/GroupApi';

	export default {
		components: {
			DocumentDrawer,
			SlicesDrawer,
			ReactDocumentTable,
		},
		data() {
			return {
				dataInitialized: false, // 数据是否初始化完成
				tabActive: 'upload',
				files: [],
				models: [],
				userGroups: [],
				config: {
					slices: {
						show: false,
						title: '',
					},
					drawer: {
						show: false,
						title: '',
					},
					loading: true,
				},
				statuses: [
					{ key: 1, value: '等待切片' },
					{ key: 2, value: '切片成功' },
					{ key: 3, value: '切片失败' },
					{ key: 4, value: '已被禁用' },
					{key: 5, value: '切片中'},
				],
				book: {},
				document: {},
				documents: [],
				documentSlices: [],
				query: {
					title: '',
					owner: '',
					knowledgeBaseId: this.$route.query.id,
					status: '',
					page: 1,
					size: 10,
				},
				paging: {
					items: [],
					size: 10,
					page: 1,
					total: 0,
				},
			};
		},
		async created() {
			// 加载所有数据
			await Promise.all([
				this.getDocuments(this.query),
				this.getBook(this.$route.query.id),
				this.getModels(),
				this.getUserGroups()
			]);

			// 数据加载完成，渲染React组件
			this.dataInitialized = true;
		},
		methods: {
			// 处理路由变化，显示选中文档的ID
			handleRouteChange(docId) {
				// 更新路由参数，显示选中的文档ID
				this.$router.push({
					path: this.$route.path,
					query: {
						...this.$route.query,
						docId: docId
					}
				});
			},
			getDocumentContent(id) {
				this.config.slices.show = true;
				documentApi.getDocumentContent(id).then((resp) => {
					if (resp.code === 200) {
						this.documentSlices = resp.data;
					}
				});
			},
			getModels() {
				return modelApi.getEmbddingModels().then((resp) => {
					if (resp.code === 200) {
						this.models = resp.data;
					}
				});
			},
			beforeUpload(file) {
				console.log('Parent beforeUpload:', file.name);
				this.files.push(file);
				return true; // 确保返回true
			},

			uploadDocuments({ file, formData, onProgress }) {
				console.log('Parent upload:', file.name);
				const body = new FormData();
				body.append('files', file);
				body.append('knowledgeBaseId', this.$route.query.id);
				documentApi
					.upload(body, {
						onUploadProgress: (progressEvent) => {
							const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
							onProgress({ percent });
						},
					})
					.then((resp) => {
						if (resp?.code === 200) {
							resp.data.forEach((item) => {
								this.documents.push({
									title: item.title,
									type: item.type,
									bosUrl: item.bosUrl,
									knowledgeBaseId: this.$route.query.id,
								});
							});
						}
					})
					.catch((error) => {
						console.error('上传失败：', error);
						this.$message.error('文件上传失败！');
					});
			},
			beforeRemove(file) {
				this.files = this.files.filter((item) => item.uid !== file.uid);
				this.documents = this.documents.filter((item) => item.title !== file.name);
			},
			// uploadDocuments(params) {
			//     try {
			//         console.log(params, 1211)
			//         const body = new FormData()
			//         body.append("files", params.file);
			//         body.append('knowledgeBaseId', this.$route.query.id)
			//         documentApi.upload(body, {
			//             onUploadProgress: progressEvent => {
			//                 const percent = Math.round(
			//                     (progressEvent.loaded * 100) / progressEvent.total
			//                 );
			//                 params.onUploadProgress({ percent: percent });
			//             }
			//         }).then((resp) => {
			//             if (resp?.code === 200) {
			//                 resp.data.forEach(item => {
			//                     this.documents.push({
			//                         title: item.title,
			//                         type: item.type,
			//                         bosUrl: item.bosUrl,
			//                         knowledgeBaseId: this.$route.query.id,
			//                     })
			//                 })
			//             }
			//         })
			//     } catch (error) {
			//         console.error("上传失败：", error);
			//         this.$message.error("文件上传失败，请重试！");
			//     }
			// },
			openDrawer(index, row) {
				const currentUser = userApi.loginUser();
				const isOwner = this.book.owner.includes(currentUser);
				let isGroupMember = false;

				if (this.book.groupId) {
					isGroupMember = this.checkUserInGroup(this.book.groupId);
				}

				if (!isOwner && !isGroupMember) {
					this.$message.error('您还不具备当前知识库的管理权限，不可导入文档');
					return;
				}

				if (row) {
					this.config.drawer.title = '修改文档';
					this.document = { ...row };
					this.document.embeddingRule = JSON.parse(this.document.embeddingRule);
					this.tabActive =
						this.document.type === 'link'
							? 'link'
							: this.document.type === 'virtual'
								? 'virtual'
								: 'upload';
					console.log(this.document.type);
				} else {
					this.document = {
						embeddingRule: {
							delimiter: this.book.embeddingRule.delimiter,
							chunkTokenNum: this.book.embeddingRule.chunkTokenNum,
						},
					};
					this.document.embeddingModelId = this.book.embeddingModelId;
					this.document.parseId = this.book.parseId;
					this.config.drawer.title = '导入文档';
				}
				this.config.drawer.show = true;
			},
			// 获取用户所在的工作组列表
			getUserGroups() {
				return groupApi.getJoinedList().then((resp) => {
					if (resp.code === 200) {
						this.userGroups = resp.data || [];
					}
				});
			},
			// 检查用户是否在指定的工作组中
			checkUserInGroup(groupId) {
				return this.userGroups.some(group => group.id === groupId);
			},
			getBook(id) {
				return bookApi.getBook(id).then((resp) => {
					if (resp.code === 200) {
						this.book = resp.data;
						this.book.embeddingRule = JSON.parse(this.book.embeddingRule);
					}
				});
			},
			getDocuments(newQuery) {
				// 如果传入了新的查询参数，更新内部query状态
				if (newQuery) {
					this.query = { ...this.query, ...newQuery };
				}
				return documentApi.getDocuments(this.query).then((resp) => {
					if (resp.code === 200) {
						this.paging = resp.data;
						this.config.loading = false;
					}
				});
			},
			statusSwitch(row) {
				if (row.status === 2 || row.status === 4) {
					const body = {
						id: row.id,
						owner: row.owner,
						status: row.status === 4 ? 2 : 4,
					};
					documentApi.statusSwitch(body).then((resp) => {
						if (resp.code === 200) {
							this.getDocuments(this.query);
						}
					});
				} else {
					this.$message.error('当前状态不支持操作');
				}
			},
			remove(id) {
				const body = {
					id: id,
					owner: userApi.loginUser(),
				};
				return documentApi.remove(body).then((resp) => {
					if (resp.code === 200) {
						this.getDocuments(this.query);
					}
					return resp;
				});
			},
			handleTabChange(newTab) {
				this.tabActive = newTab;
				console.log('Current active tab:', this.tabActive);
			},
			editOrCreate() {
				const handleSuccess = () => {
					this.getDocuments();
					this.files = [];
					this.documents = [];
					this.config.drawer.show = false;
					this.config.loading = false;
				};

				const handleError = (error) => {
					console.error('操作失败:', error);
					this.$message.error('操作失败，请重试');
					this.config.loading = false;
				};

				if (this.document.id > 0) {
					if (this.documents.length > 1) {
						this.$message.error('更新文档只能上传一个文件');
						this.config.loading = false;
						return;
					}

					if (this.documents.length === 1) {
						Object.assign(this.document, {
							title: this.documents[0].title,
							type: this.documents[0].type,
							bosUrl: this.documents[0].bosUrl,
							cronOpen: this.document.cronOpen || 0,
							cronExpression: this.document.cronExpression
						});
					}

					documentApi
						.edit(this.document)
						.then((resp) => (resp?.code === 200 ? handleSuccess() : handleError()))
						.catch(handleError);
					return;
				}

				const body = {
					knowledgeBaseId: this.$route.query.id,
					owner: userApi.loginUser(),
					embeddingRule: this.document.embeddingRule || this.book.embeddingRule,
					embeddingModelId: this.document.embeddingModelId || this.book.embeddingModelId,
				};

				switch (this.tabActive) {
					case 'upload':
						if (this.documents.length === 0) {
							this.$message.error('请选择需要导入的文件');
							this.config.loading = false;
							return false;
						}
						// 为所有文档添加cronOpen和cronExpression字段
						this.documents.forEach(doc => {
							doc.cronOpen = this.document.cronOpen || 0;
							doc.cronExpression = this.document.cronExpression;
						});
						body.documents = this.documents;
						break;

					case 'link':
						if (!this.document.sourceUrl || !this.document.title) {
							this.$message.error('请填写文档标题和URL地址');
							this.config.loading = false;
							return false;
						}
						body.documents = [
							{
								title: this.document.title,
								type: 'link',
								knowledgeBaseId: this.$route.query.id,
								sourceUrl: this.document.sourceUrl,
								cronOpen: this.document.cronOpen || 0,
								cronExpression: this.document.cronExpression,
							},
						];
						break;

					case 'virtual':
						if (!this.document.embeddingModelId || !this.document.title) {
							this.$message.error('请填写文档标题和模型ID');
							this.config.loading = false;
							return false;
						}
						body.documents = [
							{
								title: this.document.title,
								type: 'virtual',
								knowledgeBaseId: this.$route.query.id,
								embeddingModelId: this.document.embeddingModelId,
								cronOpen: this.document.cronOpen || 0,
								cronExpression: this.document.cronExpression,
							},
						];
						break;
				}
				documentApi
					.create(body)
					.then((resp) => (resp?.code === 200 ? handleSuccess() : handleError()))
					.catch(handleError);
			},
		},
	};
</script>

<style scoped>
	.app-workspace {
		margin-top: 10px;
	}
</style>

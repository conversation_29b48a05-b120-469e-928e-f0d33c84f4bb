<!-- eslint-disable -->
<template>
	<div class="app-workspace">
		<el-row class="query-container">
			<el-col :span="24">
				<el-form class="query-form" inline>
					<el-form-item label="知识库名称:">
						<el-input type="text" v-model="query.name" placeholder="根据知识库标题模糊查询" clearable />
					</el-form-item>
					<el-form-item label="所有人:">
						<el-input type="text" v-model="query.owner" placeholder="支持所有人姓名模糊查询" clearable />
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="getBooks(query)">查询</el-button>
					</el-form-item>
					<el-form-item style="float: right">
						<el-button type="primary" @click="openDialog">新建</el-button>
					</el-form-item>
				</el-form>
			</el-col>
		</el-row>
		<el-row class="table-container">
			<el-col :span="24">
				<el-table v-loading="config.loading" :data="paging.items" stripe table-layout="auto">
					<el-table-column fixed prop="id" label="ID" width="80" />
					<el-table-column prop="name" label="知识库名称" width="200" />
					<el-table-column prop="description" label="知识库描述" />
					<el-table-column prop="owner" label="创建人" width="100" />
					<el-table-column prop="createAt" label="创建时间" width="180" />
					<el-table-column label="操作" v-slot="scope" width="200">
						<el-button type="primary" size="small" @click="goToDocs(scope.row.id)" link>查看文档</el-button>
						<el-button
							type="primary"
							size="small"
							@click="openDialog(scope.$index, scope.row)"
							link
							v-if="userApi().isAdmin(scope.row.owner)"
						>
							修改
						</el-button>
						<el-popconfirm
							width="220"
							confirm-button-text="确认"
							cancel-button-text="取消"
							title="确认要删除该条记录吗?"
							v-if="userApi().isAdmin(scope.row.owner)"
							@confirm="remove(scope.row.id)"
						>
							<template #reference>
								<el-button type="danger" size="small" link>删除</el-button>
							</template>
						</el-popconfirm>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row justify="center">
			<el-pagination
				v-model:current-page="query.page"
				v-model:page-size="query.size"
				@change="getBooks(query)"
				background
				layout="prev, pager, next, jumper, total"
				:total="paging.total"
			/>
		</el-row>
		<el-dialog v-model="config.dialog.show" :title="config.dialog.title" width="600">
			<el-form class="add-edit-form" :model="book" :rules="rules" ref="bookForm">
				<el-form-item label="知识库名称:" prop="name" label-width="100px">
					<el-input
						type="text"
						v-model="book.name"
						placeholder="请输入知识库名称"
						clearable
						style="width: 400px"
					/>
				</el-form-item>
				<el-form-item label="知识库描述:" prop="description" label-width="100px">
					<el-input
						type="text"
						v-model="book.description"
						placeholder="输入知识库描述"
						clearable
						style="width: 400px"
					/>
				</el-form-item>
				<el-form-item label="切词模型" prop="embeddingModelId" label-width="100px">
					<el-select
						v-model="book.embeddingModelId"
						placeholder="请选择切词模型"
						style="width: 400px"
						clearable
					>
						<el-option
							v-for="item in models"
							:key="item.id"
							:label="`${item.platform}-${item.name}`"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="切词类型:" prop="parseId" label-width="100px">
					<el-select v-model="book.parseId" placeholder="请选择切词类型" style="width: 400px" clearable>
						<el-option
							v-for="item in dict.parserOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="切片分隔符:" prop="embeddingRule.delimiter" label-width="100px">
					<el-select
						multiple
						style="width: 400px"
						v-model="book.embeddingRule.delimiter"
						placeholder="切词分隔符，可以多个"
					>
						<el-option-group v-for="group in dict.delimiters" :key="group.label" :label="group.label">
							<el-option
								v-for="item in group.options"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-option-group>
					</el-select>
					<el-tooltip content="按照指定的标识符切分文本,可以有多个分割符" placement="top" effect="light">
						<el-icon style="margin-left: 5px">
							<InfoFilled />
						</el-icon>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="最大切片长:" prop="embeddingRule.chunkTokenNum" label-width="100px">
					<el-input
						type="text"
						v-model="book.embeddingRule.chunkTokenNum"
						placeholder="最大切片长度"
						clearable
						style="width: 400px"
					/>
					<el-tooltip
						content="切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简"
						placement="top"
						effect="light"
					>
						<el-icon style="margin-left: 5px">
							<InfoFilled />
						</el-icon>
					</el-tooltip>
				</el-form-item>
				<el-form-item label="知识管理员:" prop="owner" label-width="100px">
					<el-input
						type="text"
						v-model="book.owner"
						placeholder="指定知识库管理员，多人以英文分号分割"
						clearable
						style="width: 400px"
					/>
					<el-tooltip content="多人以英文分号分隔" placement="top" effect="light">
						<el-icon style="margin-left: 5px">
							<InfoFilled />
						</el-icon>
					</el-tooltip>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button
						@click="
							config.dialog.show = false;
							$forceUpdate();
						"
					>
						取消
					</el-button>
					<el-button type="primary" @click="editOrCreate()">保存</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import bookApi from '@/api/BookApi.ts';
import router from '@/router';
import userApi from '@/api/UserApi.ts';
import modelApi from '@/api/ModelApi.ts';
import dict from '@/utils/dict.ts';

export default {
	data() {
		return {
			config: {
				dialog: {
					show: false,
					title: '',
				},
				loading: true,
			},
			models: [],
			book: {
				name: '',
				description: '',
				owner: '',
				embeddingModelId: '',
				parseId: 1,
				embeddingRule: {
					delimiter: [],
					chunkTokenNum: 600,
				},
			},
			query: {
				name: '',
				owner: '',
				page: 1,
				size: 10,
			},
			paging: {
				page: 1,
				size: 10,
				total: 0,
			},
			rules: {
				name: [
					{
						required: true,
						max: 10,
						message: '请输入知识库名称',
						trigger: 'blur',
					},
				],
				description: [
					{
						required: true,
						max: 100,
						message: '请填写知识库描述',
						trigger: 'blur',
					},
				],
				embeddingModelId: [{ required: true, message: '请选择切词模型', trigger: 'change' }],
				parseId: [{ required: true, message: '请选择切词方式', trigger: 'change' }],
				'embeddingRule.delimiter': [{ required: true, message: '请指定切词分隔符', trigger: 'change' }],
				'embeddingRule.chunkTokenNum': [{ required: true, message: '请输入最大切片长度', trigger: 'blur' }],
				owner: [{ required: true, message: '请指定知识库管理员', trigger: 'blur' }],
			},
		};
	},
	computed: {
		dict() {
			return dict;
		},
	},
	created() {
		this.getModels();
		this.getBooks(this.query);
	},
	methods: {
		userApi() {
			return userApi;
		},
		goToDocs(id: number) {
			router.push({ path: '/knowledge/docs', query: { id: id } });
		},
		remove(id) {
			const body = {
				id: id,
				owner: userApi.loginUser(),
			};
			bookApi.remove(body).then((resp) => {
				if (resp?.code === 200) {
					this.getBooks(this.query);
				}
			});
		},
		openDialog(index, row) {
			if (row) {
				this.config.dialog.title = '编辑知识库';
				this.book = { ...row };
				// 将json串转一下
				this.book.embeddingRule = JSON.parse(this.book.embeddingRule);
			} else {
				this.book = {
					name: '',
					description: '',
					owner: userApi.loginUser(),
					embeddingModelId: null,
					embeddingRule: {
						delimiter: [],
						chunkTokenNum: 600,
					},
				};
				this.config.dialog.title = '新建知识库';
			}
			this.config.dialog.show = true;
		},
		getModels() {
			modelApi.getEmbddingModels().then((resp) => {
				if (resp.code === 200) {
					this.models = resp.data;
				}
			});
		},
		getBooks(query) {
			this.config.loading = true;
			bookApi.getBooks(query).then((resp) => {
				if (resp?.code === 200) {
					this.paging = resp.data;
				}
				this.config.loading = false;
			});
		},
		editOrCreate: function () {
			this.$refs.bookForm.validate((valid) => {
				if (!valid) {
					return;
				}
				if (this.book.id > 0) {
					bookApi.edit(this.book).then((resp) => {
						if (resp?.code === 200) {
							this.getBooks(this.query);
						}
					});
				} else {
					bookApi.create(this.book).then((resp) => {
						if (resp?.code === 200) {
							this.getBooks(this.query);
						}
					});
				}
				this.config.dialog.show = false;
			});
		},
	},
};
</script>

<style scoped>
@import '@/assets/global.css';
</style>

<!-- eslint-disable -->
<template>
	<div class="app-workspace">
		<el-tabs v-model="tabActive" class="knowledge-tabs" @tab-click="handleTabClick">
			<el-tab-pane label="我的" name="mine" :lazy="true" />
			<el-tab-pane label="全部" name="all" :lazy="true" />
		</el-tabs>

		<knowledge-query-form 
			:query="queryParams" 
			:workgroups="workgroups" 
			:workgroups-loading="workgroupsLoading"
			:tab-active="tabActive"
			@search="handleSearch" 
			@create="handleCreate" 
		/>

		<knowledge-table
			:table-data="paging.items"
			:loading="loading"
			:paging="paging"
			:workgroups="workgroups"
			@view-docs="handleViewDocs"
			@edit="handleEdit"
			@delete="handleDelete"
			@update:page="handlePageChange"
			@update:size="handleSizeChange"
		/>

		<knowledge-dialog
			v-model="dialogVisible"
			:title="dialogTitle"
			:book="currentBook"
			:models="models"
			:workgroups="workgroups"
			:workgroups-loading="workgroupsLoading"
			@save="handleSave"
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import KnowledgeQueryForm from '@/views/knowledge/Books/components/KnowledgeQueryForm.vue';
import KnowledgeTable from '@/views/knowledge/Books/components/KnowledgeTable.vue';
import KnowledgeDialog from '@/views/knowledge/Books/components/KnowledgeDialog.vue';
import bookApi from '@/api/BookApi';
import modelApi from '@/api/ModelApi';
import userApi from '@/api/UserApi';
import groupApi from '@/api/GroupApi';
import type { Book, Paging, QueryParams } from './components/types';

const router = useRouter();

// 状态管理
const loading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref('');
const tabActive = ref('mine');
const workgroups = ref<any[]>([]);
const workgroupsLoading = ref(false);

const queryParams = reactive<QueryParams>({
	name: '',
	owner: '',
	page: 1,
	size: 10,
	type: 1, // 默认查询个人知识库
});

const paging = reactive<Paging>({
	page: 1,
	size: 10,
	total: 0,
	items: [],
});

const currentBook = ref<Book>({
	name: '',
	description: '',
	owner: '',
	embeddingModelId: '',
	parseId: 1,
	groupId: null,
	embeddingRule: {
		delimiter: [],
		chunkTokenNum: 600,
	},
});

const models = ref<any[]>([]);

// 生命周期钩子
onMounted(() => {
	getModels();
	getBooks();
	fetchWorkgroups();
});

// 方法定义
// 获取工作组列表
const fetchWorkgroups = async () => {
	workgroupsLoading.value = true;
	try {
		const response = await groupApi.getJoinedList();
		workgroups.value = response?.data || [];
	} catch (error) {
		console.error('获取工作组列表错误:', error);
		ElMessage.error('获取工作组列表失败');
	} finally {
		workgroupsLoading.value = false;
	}
};

const handleTabClick = (tab: { props: { name: string } }) => {
	queryParams.type = tab.props.name === 'all' ? 0 : 1;
	queryParams.page = 1;
	getBooks();
};

const handlePageChange = (page: number) => {
	queryParams.page = page;
	getBooks();
};

const handleSizeChange = (size: number) => {
	queryParams.size = size;
	queryParams.page = 1;
	getBooks();
};
const getModels = async () => {
	try {
		const resp = await modelApi.getEmbddingModels();
		if (resp.code === 200) {
			models.value = resp.data;
		}
	} catch (error) {
		console.error('Failed to fetch models:', error);
	}
};

const getBooks = async (query: QueryParams = queryParams) => {
	loading.value = true;
	try {
		const resp = await bookApi.getBooks(query);
		if (resp?.code === 200) {
			Object.assign(paging, resp.data);
		}
	} catch (error) {
		console.error('Failed to fetch books:', error);
	} finally {
		loading.value = false;
	}
};

const handleSearch = () => {
	queryParams.page = 1;
	getBooks();
};

const handleCreate = () => {
	dialogTitle.value = '新建知识库';
	currentBook.value = {
		name: '',
		description: '',
		owner: userApi.loginUser(),
		embeddingModelId: '',
		parseId: 1,
		groupId: null,
		embeddingRule: {
			delimiter: [],
			chunkTokenNum: 600,
		},
	};
	dialogVisible.value = true;
};

const handleEdit = (book: Book) => {
	dialogTitle.value = '编辑知识库';
	currentBook.value = {
		...book,
		embeddingRule: typeof book.embeddingRule === 'string' ? JSON.parse(book.embeddingRule) : book.embeddingRule,
	};
	dialogVisible.value = true;
};

const handleSave = async (book: Book) => {
	try {
		const apiMethod = book.id ? bookApi.edit : bookApi.create;
		const resp = await apiMethod(book);
		if (resp?.code === 200) {
			getBooks();
			dialogVisible.value = false;
			ElMessage.success(resp?.message || '操作成功');
		}
	} catch (error) {
		console.error('Failed to save book:', error);
	}
};

const handleViewDocs = (id: number) => {
	router.push({ path: '/knowledge/docs', query: { id } });
};

const handleDelete = async (id: number) => {
	try {
		const resp = await bookApi.remove({ id, owner: userApi.loginUser() });
		if (resp?.code === 200) {
			getBooks();
		}
	} catch (error) {
		console.error('Failed to delete book:', error);
	}
};
</script>

<style scoped>
.app-workspace {
	padding: 20px;
}

.knowledge-tabs {
	margin-bottom: 20px;
}

.knowledge-tabs > .el-tabs__content {
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}
</style>

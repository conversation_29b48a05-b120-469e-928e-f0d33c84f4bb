import pluginVue from 'eslint-plugin-vue';
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript';
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting';

export default defineConfigWithVueTs(
	{
		rules: {
			'vue/multi-word-component-names': 'off',
			'vue/max-attributes-per-line': [
				'error',
				{
					singleline: 4,
					multiline: {
						max: 1,
						allowFirstLine: false,
					},
				},
			],
			'prettier/prettier': ['error', {}, { usePrettierrc: true }], // 确保 ESLint 使用 Prettier 配置
			'vue/html-closing-bracket-spacing': 'off',
			'vue/html-closing-bracket-newline': [
				'error',
				{
					singleline: 'never', // 单行标签的 `/>` 前不能换行
					multiline: 'always', // 多行标签的 `>` 必须换行
				},
			],
			'vue/attribute-order': [
				'error',
				{
					order: [
						'DEFINITION', // is
						'LIST_RENDERING', // v-for
						'CONDITIONALS', // v-if, v-else-if, v-else, v-show, v-cloak
						'RENDER_MODIFIERS', // v-pre, v-once
						'GLOBAL', // id
						'UNIQUE', // ref, key
						'SLOT', // v-slot, slot
						'TWO_WAY_BINDING', // v-model
						'OTHER_DIRECTIVES', // v-custom-directive
						'OTHER_ATTR', // 其他属性
						'EVENTS', // v-on
						'CONTENT', // v-text, v-html
					],
					alphabetical: false, // 不强制字母顺序
				},
			],
			'vue/html-indent': [
				'error',
				4,
				{
					attribute: 1,
					baseIndent: 1,
					closeBracket: 0,
					alignAttributesVertically: true,
				},
			],
		},
	},
	{
		name: 'app/files-to-lint',
		files: ['**/*.{ts,mts,tsx,vue}'],
	},
	{
		name: 'app/files-to-ignore',
		ignores: ['**/dist/**', '**/dist-ssr/**', '**/coverage/**'],
	},

	pluginVue.configs['flat/essential'],
	vueTsConfigs.recommended,
	skipFormatting,
);
